#!/usr/bin/env py
# -*- coding: utf-8 -*-

import pandas as pd
import requests
import json
import base64
import re
from datetime import datetime, timedelta, timezone
from collections import defaultdict

# JIRA Configuration
JIRA_URL = "https://read-at-sea.atlassian.net"
JIRA_EMAIL = "j.w<PERSON><PERSON><PERSON>@a-soft.pl"
JIRA_API_TOKEN = "ATATT3xFfGF0o1tVy4he6mvThd2U0mZU2NCqHbZoCB216R6aPWZZUnKecNaRaTIFIBkmb3sTHDvEPtjnJ__aW1EGolFwrIFPuo5qY9Z4SEW2TlMMt8d8v0sLWKkGGMkjLSDQQyP2wT4BImUn_XaNGozE1-KzMj1GFxhAtdkdmwtMErw0hkwPo4Q=D1D6CC05"

# Bitbucket Configuration
BITBUCKET_USERNAME = "mkasoft"
BITBUCKET_APP_PASSWORD = "ATBBspuHWSzDP9kUnNBRJWbLyGAP7E2DE5BA"
BITBUCKET_WORKSPACE = "a-softy"

# Repositories to analyze
REPOSITORIES = ["trust-netcore", "trust-react", "trust-azure-functions"]

def get_all_jira_issues():
    """Retrieves all issues from both JIRA projects (RASEA and RS)"""
    
    auth_string = f"{JIRA_EMAIL}:{JIRA_API_TOKEN}"
    auth_bytes = auth_string.encode('ascii')
    auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
    
    headers = {
        'Authorization': f'Basic {auth_b64}',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }
    
    all_issues = {}
    projects = ['RASEA', 'RS']
    
    for project in projects:
        print(f"Retrieving issues from JIRA project {project}...")
        
        url = f"{JIRA_URL}/rest/api/3/search"
        start_at = 0
        max_results = 50
        
        while True:
            params = {
                'jql': f'project = {project} ORDER BY created DESC',
                'startAt': start_at,
                'maxResults': max_results,
                'fields': 'key,summary,status,assignee,reporter,created,updated,priority,issuetype,description,components,labels,comment'
            }
            
            try:
                response = requests.get(url, headers=headers, params=params)
                response.raise_for_status()
                data = response.json()
                
                issues = data.get('issues', [])
                
                for issue in issues:
                    issue_key = issue['key']
                    fields = issue.get('fields', {})
                    
                    # Process description
                    if fields.get('description'):
                        fields['description_text'] = extract_text_from_jira_content(fields['description'])
                    
                    # Process comments
                    comments_text = []
                    if fields.get('comment', {}).get('comments'):
                        for comment in fields['comment']['comments']:
                            if comment.get('body'):
                                comment_text = extract_text_from_jira_content(comment['body'])
                                author = comment.get('author', {}).get('displayName', 'Unknown')
                                created = comment.get('created', '')
                                comments_text.append(f"[{author}, {created[:10]}]: {comment_text}")
                    
                    fields['comments_text'] = comments_text
                    fields['all_comments_combined'] = ' | '.join(comments_text)
                    
                    all_issues[issue_key] = fields
                
                total = data.get('total', 0)
                print(f"Retrieved {len(issues)} issues from {project}, total: {len([k for k in all_issues.keys() if k.startswith(project)])}/{total}")
                
                if start_at + len(issues) >= total or len(issues) == 0:
                    break
                    
                start_at += max_results
                
            except requests.exceptions.RequestException as e:
                print(f"Error retrieving data from JIRA project {project}: {e}")
                break
    
    return all_issues

def get_all_bitbucket_commits():
    """Retrieves all commits from Bitbucket repositories (last 6 months)"""
    
    auth_string = f"{BITBUCKET_USERNAME}:{BITBUCKET_APP_PASSWORD}"
    auth_bytes = auth_string.encode('ascii')
    auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
    
    headers = {
        'Authorization': f'Basic {auth_b64}',
        'Accept': 'application/json'
    }
    
    all_commits = []
    six_months_ago = datetime.now(timezone.utc) - timedelta(days=180)
    
    for repo in REPOSITORIES:
        print(f"Retrieving commits from {repo}...")
        
        url = f"https://api.bitbucket.org/2.0/repositories/{BITBUCKET_WORKSPACE}/{repo}/commits/master"
        
        while url:
            try:
                response = requests.get(url, headers=headers)
                response.raise_for_status()
                data = response.json()
                
                commits = data.get('values', [])
                
                for commit in commits:
                    commit_date = datetime.fromisoformat(commit['date'].replace('Z', '+00:00'))
                    
                    if commit_date < six_months_ago:
                        break  # Stop when we reach older commits
                    
                    commit['repository'] = repo
                    all_commits.append(commit)
                
                url = data.get('next')
                
            except requests.exceptions.RequestException as e:
                print(f"Error fetching commits from {repo}: {e}")
                break
    
    print(f"Retrieved {len(all_commits)} total commits from all repositories")
    return all_commits

def extract_text_from_jira_content(content):
    """Extracts text from JIRA content structure (JSON)"""
    if not content:
        return ""
    
    if isinstance(content, str):
        return content
    
    if isinstance(content, dict):
        text_parts = []
        
        if content.get('type') == 'doc' and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))
        elif content.get('type') == 'paragraph' and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))
        elif content.get('type') == 'text' and 'text' in content:
            text_parts.append(content['text'])
        elif content.get('type') in ['heading', 'codeBlock', 'blockquote'] and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))
        elif content.get('type') in ['bulletList', 'orderedList'] and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))
        elif content.get('type') == 'listItem' and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))
        
        return ' '.join(text_parts)
    
    elif isinstance(content, list):
        text_parts = []
        for item in content:
            text_parts.append(extract_text_from_jira_content(item))
        return ' '.join(text_parts)
    
    return str(content)

def extract_jira_keys(text):
    """Extracts JIRA keys from text"""
    if pd.isna(text) or not isinstance(text, str):
        return []
    
    patterns = [
        r'\b(RS-\d+)\b',
        r'\b(RASEA-\d+)\b',
        r'\b([A-Z]+-\d+)\b'
    ]
    
    jira_keys = []
    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        jira_keys.extend(matches)
    
    return list(set(jira_keys))

def find_related_commits(jira_keys, simon_comment, task_description, all_commits):
    """Finds commits related to JIRA keys or contextually related"""
    
    related_commits = []
    
    # First, find commits with exact JIRA matches
    for commit in all_commits:
        message = commit.get('message', '')
        commit_jira_keys = extract_jira_keys(message)
        
        # Check for exact JIRA matches
        if any(jira_key in commit_jira_keys for jira_key in jira_keys):
            related_commits.append({
                'type': 'exact_jira',
                'commit': commit,
                'matching_jira': [jk for jk in jira_keys if jk in commit_jira_keys]
            })
    
    # If no exact matches, try contextual matching
    if not related_commits and simon_comment:
        keywords = extract_keywords(simon_comment + " " + task_description)
        
        for commit in all_commits:
            message = commit.get('message', '').lower()
            score = sum(1 for keyword in keywords if keyword in message)
            
            if score > 0:
                related_commits.append({
                    'type': 'contextual',
                    'commit': commit,
                    'score': score,
                    'keywords': keywords
                })
        
        # Sort contextual matches by score
        related_commits = sorted(related_commits, key=lambda x: x.get('score', 0), reverse=True)[:3]
    
    return related_commits

def extract_keywords(text):
    """Extracts meaningful keywords from text"""
    if not text:
        return []
    
    words = re.findall(r'\b\w+\b', text.lower())
    common_words = {'this', 'that', 'with', 'from', 'they', 'have', 'been', 'were', 'said', 'each', 'which', 'their', 'time', 'will', 'about', 'would', 'there', 'could', 'other', 'more', 'very', 'what', 'know', 'just', 'first', 'into', 'over', 'think', 'also', 'your', 'work', 'life', 'only', 'can', 'still', 'should', 'after', 'being', 'now', 'made', 'before', 'here', 'through', 'when', 'where', 'much', 'some', 'these', 'many', 'then', 'them', 'well'}
    
    return [word for word in words if len(word) > 3 and word not in common_words]

def create_business_description_from_commit(commit_message):
    """Creates comprehensive business-friendly description from commit message"""

    message_lower = commit_message.lower()

    # Enhanced patterns with business impact descriptions
    patterns = {
        r'fix|bug|error|issue|hotfix|cargo fix|firstmeterreading': {
            'category': 'Critical System Issue Resolution',
            'impact': 'Ensures system reliability and user satisfaction',
            'business_value': 'Maintains operational continuity'
        },
        r'add|new|create|implement|feature': {
            'category': 'New Feature Development',
            'impact': 'Expands system capabilities and user functionality',
            'business_value': 'Increases competitive advantage and user value'
        },
        r'update|modify|change|improve|enhance': {
            'category': 'System Enhancement',
            'impact': 'Improves existing functionality and user experience',
            'business_value': 'Optimizes operational efficiency'
        },
        r'remove|delete|clean|optimization': {
            'category': 'Code Optimization',
            'impact': 'Reduces technical debt and improves maintainability',
            'business_value': 'Lowers long-term maintenance costs'
        },
        r'refactor|restructure|quality': {
            'category': 'Code Quality Improvement',
            'impact': 'Enhances code maintainability and scalability',
            'business_value': 'Reduces future development costs'
        },
        r'test|spec|unit|validation|verify': {
            'category': 'Quality Assurance Enhancement',
            'impact': 'Ensures system reliability and reduces defects',
            'business_value': 'Minimizes production issues and support costs'
        },
        r'config|setup|deploy|release|merge.*release': {
            'category': 'System Deployment & Release',
            'impact': 'Delivers new functionality to production environment',
            'business_value': 'Provides immediate value to end users'
        },
        r'ui|interface|frontend|react|user|form|modal|page': {
            'category': 'User Interface Enhancement',
            'impact': 'Improves user experience and interface usability',
            'business_value': 'Increases user satisfaction and adoption'
        },
        r'api|backend|service|netcore|server|database': {
            'category': 'Backend Service Enhancement',
            'impact': 'Strengthens system architecture and data processing',
            'business_value': 'Improves system performance and scalability'
        },
        r'security|auth|login|permission|access': {
            'category': 'Security Enhancement',
            'impact': 'Strengthens system security and data protection',
            'business_value': 'Ensures compliance and reduces security risks'
        },
        r'performance|optimize|speed|efficiency|monitoring': {
            'category': 'Performance Optimization',
            'impact': 'Improves system speed and resource utilization',
            'business_value': 'Enhances user experience and reduces costs'
        },
        r'merge|pull request|pr|integration': {
            'category': 'Code Integration & Collaboration',
            'impact': 'Consolidates development work and maintains code quality',
            'business_value': 'Ensures coordinated development progress'
        },
        r'cargo|vessel|ship|maritime': {
            'category': 'Maritime Operations Enhancement',
            'impact': 'Improves shipping and cargo management functionality',
            'business_value': 'Optimizes maritime business operations'
        },
        r'emission|environmental|compliance': {
            'category': 'Environmental Compliance Enhancement',
            'impact': 'Ensures regulatory compliance and environmental reporting',
            'business_value': 'Meets legal requirements and sustainability goals'
        },
        r'meter|reading|measurement|monitoring': {
            'category': 'Monitoring System Enhancement',
            'impact': 'Improves data collection and measurement accuracy',
            'business_value': 'Provides better operational insights'
        },
        r'report|export|download|analytics': {
            'category': 'Reporting & Analytics Enhancement',
            'impact': 'Enhances data analysis and reporting capabilities',
            'business_value': 'Improves decision-making and compliance'
        },
        r'notification|email|alert|communication': {
            'category': 'Communication System Enhancement',
            'impact': 'Improves system notifications and user communication',
            'business_value': 'Enhances user engagement and system transparency'
        }
    }

    for pattern, details in patterns.items():
        if re.search(pattern, message_lower):
            return details

    return {
        'category': 'General Development Activity',
        'impact': 'Contributes to overall system development',
        'business_value': 'Supports continuous system improvement'
    }

def estimate_work_effort(simon_comment, jira_info, bitbucket_info, task_description):
    """Estimates work effort in hours based on complexity indicators"""

    effort_hours = 0
    complexity_factors = []

    # Base effort from Simon's comment type
    simon_lower = simon_comment.lower() if simon_comment else ""

    if re.search(r'bug|error|issue|problem|fix|broken|not working|fail', simon_lower):
        effort_hours += 2  # Bug investigation and fix
        complexity_factors.append("Bug investigation")
    elif re.search(r'improve|enhance|add|new|feature|change|better', simon_lower):
        effort_hours += 4  # Feature development
        complexity_factors.append("Feature development")
    elif re.search(r'question|explain|clarify|understand|what|how|why', simon_lower):
        effort_hours += 0.5  # Documentation/explanation
        complexity_factors.append("Analysis and documentation")
    elif re.search(r'test|testing|verify|check|validation', simon_lower):
        effort_hours += 1  # Testing effort
        complexity_factors.append("Testing and validation")

    # Additional effort from JIRA complexity
    if jira_info and isinstance(jira_info, str):
        if 'Priority: High' in jira_info or 'Priority: Critical' in jira_info:
            effort_hours += 2
            complexity_factors.append("High priority")
        if 'Type: Epic' in jira_info:
            effort_hours += 8
            complexity_factors.append("Epic scope")
        if 'Type: Story' in jira_info:
            effort_hours += 3
            complexity_factors.append("User story")
        if 'Comments (' in jira_info:
            # Count comments as indicator of complexity
            comment_count = len(re.findall(r'Comments \((\d+)\)', jira_info))
            if comment_count > 0:
                effort_hours += min(comment_count * 0.5, 3)
                complexity_factors.append(f"Multiple discussions ({comment_count} comments)")

    # Additional effort from Bitbucket activity
    if bitbucket_info and isinstance(bitbucket_info, str):
        commit_count = len(re.findall(r'\*\*trust-', bitbucket_info))
        if commit_count > 1:
            effort_hours += min(commit_count * 0.5, 4)
            complexity_factors.append(f"Multiple commits ({commit_count})")

        if 'System Issue Resolution' in bitbucket_info:
            effort_hours += 1
            complexity_factors.append("System issue resolution")
        if 'New Feature Development' in bitbucket_info:
            effort_hours += 2
            complexity_factors.append("Feature development")
        if 'Backend Service Enhancement' in bitbucket_info:
            effort_hours += 1.5
            complexity_factors.append("Backend complexity")
        if 'User Interface Enhancement' in bitbucket_info:
            effort_hours += 1
            complexity_factors.append("UI/UX work")

    # Task description complexity
    if task_description and isinstance(task_description, str):
        desc_lower = task_description.lower()
        if re.search(r'performance|optimization|refactor|migration', desc_lower):
            effort_hours += 3
            complexity_factors.append("Performance/optimization work")
        if re.search(r'integration|api|database|security', desc_lower):
            effort_hours += 2
            complexity_factors.append("Integration complexity")
        if re.search(r'report|calculation|algorithm', desc_lower):
            effort_hours += 2
            complexity_factors.append("Complex calculations")

    # Minimum and maximum bounds
    effort_hours = max(0.25, min(effort_hours, 40))  # Between 15 minutes and 40 hours

    # Round to reasonable increments
    if effort_hours <= 1:
        effort_hours = round(effort_hours * 4) / 4  # Quarter hour increments
    elif effort_hours <= 8:
        effort_hours = round(effort_hours * 2) / 2   # Half hour increments
    else:
        effort_hours = round(effort_hours)           # Hour increments

    return effort_hours, complexity_factors

def translate_to_english(text):
    """Translates Polish terms to English with comprehensive dictionary"""
    if pd.isna(text) or not isinstance(text, str):
        return text

    translations = {
        # Basic terms
        'Projekt': 'Project',
        'Klient': 'Client',
        'Opis': 'Description',
        'Zadanie': 'Task',
        'Użytkownik': 'User',
        'Grupa': 'Group',
        'Tagi': 'Tags',
        'Płatne': 'Billable',
        'Data rozpoczęcia': 'Start Date',
        'Godzina rozpoczęcia': 'Start Time',
        'Data zakończenia': 'End Date',
        'Godzina zakończenia': 'End Time',
        'Czas trwania (h)': 'Duration (h)',
        'Czas trwania (dziesiętny)': 'Duration (decimal)',
        'Stawka płatna (USD)': 'Billable Rate (USD)',
        'Kwota płatna (USD)': 'Billable Amount (USD)',
        'Kategoria': 'Category',
        'Bug': 'Bug',
        'Zadanie': 'Task',
        'Usprawnienie': 'Improvement',
        'Pytanie': 'Question',
        'Test': 'Test',
        'Tak': 'Yes',
        'Nie': 'No',
        'ReadAtSea': 'ReadAtSea',
        'Trust': 'Trust',

        # JIRA specific terms
        'wyszukiwania': 'search functionality',
        'Usprawnienie wyszukiwania': 'Search Enhancement',
        'Dodawanie parametrów wyszukiwania do url': 'Adding search parameters to URL',
        'parametrów wyszukiwania': 'search parameters',
        'wyszukiwanie': 'search',
        'parametry': 'parameters',
        'filtrowanie': 'filtering',
        'sortowanie': 'sorting',
        'raport': 'report',
        'raporty': 'reports',
        'wyniki': 'results',
        'konsumpcja': 'consumption',
        'wydajność': 'performance',
        'różnica': 'difference',
        'różnice': 'differences',
        'obliczenia': 'calculations',
        'obliczanie': 'calculation',
        'dane': 'data',
        'wyświetlanie': 'display',
        'pokazywanie': 'showing',
        'formularz': 'form',
        'formularze': 'forms',
        'walidacja': 'validation',
        'sprawdzanie': 'validation',
        'błąd': 'error',
        'błędy': 'errors',
        'problem': 'issue',
        'problemy': 'issues',
        'poprawka': 'fix',
        'poprawki': 'fixes',
        'naprawa': 'repair',
        'aktualizacja': 'update',
        'aktualizacje': 'updates',
        'ulepszenie': 'enhancement',
        'ulepszenia': 'enhancements',
        'funkcjonalność': 'functionality',
        'funkcje': 'functions',
        'funkcja': 'function',
        'interfejs': 'interface',
        'użytkownika': 'user',
        'strona': 'page',
        'strony': 'pages',
        'ekran': 'screen',
        'ekrany': 'screens',
        'menu': 'menu',
        'przycisk': 'button',
        'przyciski': 'buttons',
        'pole': 'field',
        'pola': 'fields',
        'tabela': 'table',
        'tabele': 'tables',
        'lista': 'list',
        'listy': 'lists',
        'kolumna': 'column',
        'kolumny': 'columns',
        'wiersz': 'row',
        'wiersze': 'rows',
        'wartość': 'value',
        'wartości': 'values',
        'konfiguracja': 'configuration',
        'ustawienia': 'settings',
        'opcje': 'options',
        'wybór': 'selection',
        'wybieranie': 'selecting',
        'edycja': 'editing',
        'edytowanie': 'editing',
        'dodawanie': 'adding',
        'usuwanie': 'removing',
        'kasowanie': 'deleting',
        'zapisywanie': 'saving',
        'ładowanie': 'loading',
        'pobieranie': 'fetching',
        'wysyłanie': 'sending',
        'przesyłanie': 'transmitting',
        'logowanie': 'logging',
        'autoryzacja': 'authorization',
        'uwierzytelnianie': 'authentication',
        'uprawnienia': 'permissions',
        'dostęp': 'access',
        'bezpieczeństwo': 'security',
        'szyfrowanie': 'encryption',
        'baza danych': 'database',
        'bazy danych': 'databases',
        'serwer': 'server',
        'serwery': 'servers',
        'aplikacja': 'application',
        'aplikacje': 'applications',
        'system': 'system',
        'systemy': 'systems',
        'moduł': 'module',
        'moduły': 'modules',
        'komponent': 'component',
        'komponenty': 'components',
        'usługa': 'service',
        'usługi': 'services',
        'API': 'API',
        'endpoint': 'endpoint',
        'żądanie': 'request',
        'żądania': 'requests',
        'odpowiedź': 'response',
        'odpowiedzi': 'responses',
        'komunikacja': 'communication',
        'integracja': 'integration',
        'synchronizacja': 'synchronization',
        'import': 'import',
        'eksport': 'export',
        'migracja': 'migration',
        'backup': 'backup',
        'kopia zapasowa': 'backup',
        'przywracanie': 'restore',
        'monitoring': 'monitoring',
        'logowanie': 'logging',
        'debugowanie': 'debugging',
        'testowanie': 'testing',
        'testy': 'tests',
        'weryfikacja': 'verification',
        'sprawdzenie': 'check',
        'kontrola': 'control',
        'zarządzanie': 'management',
        'administracja': 'administration',
        'konfigurowanie': 'configuring',
        'instalacja': 'installation',
        'wdrożenie': 'deployment',
        'publikacja': 'publication',
        'wydanie': 'release',
        'wersja': 'version',
        'wersje': 'versions',
        'aktualizowanie': 'updating',
        'modernizacja': 'modernization',
        'optymalizacja': 'optimization',
        'wydajność': 'performance',
        'szybkość': 'speed',
        'czas': 'time',
        'czas odpowiedzi': 'response time',
        'czas ładowania': 'loading time',
        'przepustowość': 'throughput',
        'skalowalność': 'scalability',
        'dostępność': 'availability',
        'niezawodność': 'reliability',
        'stabilność': 'stability',
        'jakość': 'quality',
        'standard': 'standard',
        'standardy': 'standards',
        'zgodność': 'compliance',
        'certyfikacja': 'certification',
        'dokumentacja': 'documentation',
        'instrukcja': 'instruction',
        'instrukcje': 'instructions',
        'przewodnik': 'guide',
        'pomoc': 'help',
        'wsparcie': 'support',
        'obsługa': 'support',
        'serwis': 'service',
        'konserwacja': 'maintenance',
        'utrzymanie': 'maintenance',
        'rozwój': 'development',
        'programowanie': 'programming',
        'kodowanie': 'coding',
        'implementacja': 'implementation',
        'realizacja': 'implementation',
        'wykonanie': 'execution',
        'uruchomienie': 'launch',
        'start': 'start',
        'zatrzymanie': 'stop',
        'pauza': 'pause',
        'wznowienie': 'resume',
        'restart': 'restart',
        'reset': 'reset',
        'przywrócenie': 'restore',
        'cofnięcie': 'rollback',
        'anulowanie': 'cancellation',
        'odwołanie': 'cancellation',
        'potwierdzenie': 'confirmation',
        'akceptacja': 'acceptance',
        'zatwierdzenie': 'approval',
        'odrzucenie': 'rejection',
        'odmowa': 'denial',
        'zgoda': 'consent',
        'pozwolenie': 'permission',
        'zezwolenie': 'authorization',
        'licencja': 'license',
        'umowa': 'contract',
        'kontrakt': 'contract',
        'projekt': 'project',
        'projekty': 'projects',
        'zadanie': 'task',
        'zadania': 'tasks',
        'cel': 'goal',
        'cele': 'goals',
        'wymaganie': 'requirement',
        'wymagania': 'requirements',
        'specyfikacja': 'specification',
        'opis': 'description',
        'definicja': 'definition',
        'charakterystyka': 'characteristics',
        'właściwości': 'properties',
        'atrybuty': 'attributes',
        'parametry': 'parameters',
        'zmienne': 'variables',
        'stałe': 'constants',
        'konfiguracja': 'configuration',
        'ustawienia': 'settings',
        'preferencje': 'preferences',
        'opcje': 'options',
        'wybory': 'choices',
        'alternatywy': 'alternatives',
        'możliwości': 'possibilities',
        'warianty': 'variants',
        'scenariusze': 'scenarios',
        'przypadki': 'cases',
        'sytuacje': 'situations',
        'warunki': 'conditions',
        'ograniczenia': 'limitations',
        'restrykcje': 'restrictions',
        'zasady': 'rules',
        'reguły': 'rules',
        'polityki': 'policies',
        'procedury': 'procedures',
        'procesy': 'processes',
        'przepływy': 'flows',
        'workflow': 'workflow',
        'cykl': 'cycle',
        'etap': 'stage',
        'etapy': 'stages',
        'faza': 'phase',
        'fazy': 'phases',
        'krok': 'step',
        'kroki': 'steps',
        'działanie': 'action',
        'działania': 'actions',
        'operacja': 'operation',
        'operacje': 'operations',
        'funkcjonowanie': 'functioning',
        'działanie': 'operation',
        'praca': 'work',
        'funkcja': 'function',
        'metoda': 'method',
        'sposób': 'method',
        'technika': 'technique',
        'podejście': 'approach',
        'strategia': 'strategy',
        'plan': 'plan',
        'plany': 'plans',
        'harmonogram': 'schedule',
        'terminy': 'deadlines',
        'deadline': 'deadline',
        'czas': 'time',
        'data': 'date',
        'daty': 'dates',
        'okres': 'period',
        'czas trwania': 'duration',
        'długość': 'length',
        'rozmiar': 'size',
        'wielkość': 'size',
        'ilość': 'quantity',
        'liczba': 'number',
        'suma': 'sum',
        'całość': 'total',
        'razem': 'total',
        'łącznie': 'total',
        'ogółem': 'total',
        'wszystko': 'all',
        'każdy': 'each',
        'wszystkie': 'all',
        'część': 'part',
        'części': 'parts',
        'element': 'element',
        'elementy': 'elements',
        'składnik': 'component',
        'składniki': 'components',
        'fragment': 'fragment',
        'fragmenty': 'fragments',
        'sekcja': 'section',
        'sekcje': 'sections',
        'rozdział': 'chapter',
        'rozdziały': 'chapters',
        'kategoria': 'category',
        'kategorie': 'categories',
        'typ': 'type',
        'typy': 'types',
        'rodzaj': 'type',
        'rodzaje': 'types',
        'klasa': 'class',
        'klasy': 'classes',
        'grupa': 'group',
        'grupy': 'groups',
        'zestaw': 'set',
        'zestawy': 'sets',
        'kolekcja': 'collection',
        'kolekcje': 'collections',
        'zbiór': 'collection',
        'zbiory': 'collections'
    }

    result = str(text)
    # Sort by length (longest first) to avoid partial replacements
    for polish, english in sorted(translations.items(), key=lambda x: len(x[0]), reverse=True):
        result = result.replace(polish, english)

    return result

def analyze_simon_row(row, all_jira_issues, all_commits):
    """Analyzes a row where Simon added a comment"""
    
    simon_comment = str(row.get('Simon', ''))
    if pd.isna(simon_comment) or simon_comment.strip() == '' or simon_comment == 'nan':
        return None
    
    # Get row data
    task_description = str(row.get('Description', ''))
    task_name = str(row.get('Task', ''))
    
    # Extract JIRA keys from all row data
    all_row_text = f"{simon_comment} {task_description} {task_name}"
    for col, value in row.items():
        if pd.notna(value) and isinstance(value, str):
            all_row_text += f" {value}"
    
    jira_keys = extract_jira_keys(all_row_text)
    
    # Prepare analysis
    analysis = {
        'simon_comment': simon_comment,
        'jira_info': '',
        'bitbucket_info': '',
        'combined_analysis': ''
    }
    
    # JIRA Analysis with translation
    if jira_keys:
        jira_info = "**JIRA TASK INFORMATION:**\n\n"

        for jira_key in jira_keys:
            if jira_key in all_jira_issues:
                fields = all_jira_issues[jira_key]
                project = "RASEA" if jira_key.startswith("RASEA") else "RS"

                jira_info += f"**{jira_key}** (Project: {project})\n"

                # Translate title
                title = translate_to_english(fields.get('summary', 'No title'))
                jira_info += f"• Title: {title}\n"

                # Translate status
                status = translate_to_english(fields.get('status', {}).get('name', 'Unknown'))
                jira_info += f"• Status: {status}\n"

                # Translate type
                issue_type = translate_to_english(fields.get('issuetype', {}).get('name', 'Unknown'))
                jira_info += f"• Type: {issue_type}\n"

                # Translate priority
                priority = translate_to_english(fields.get('priority', {}).get('name', 'Unknown'))
                jira_info += f"• Priority: {priority}\n"

                if fields.get('assignee'):
                    jira_info += f"• Assigned to: {fields['assignee'].get('displayName', 'Unknown')}\n"

                if fields.get('description_text'):
                    # Translate description
                    desc_translated = translate_to_english(fields['description_text'])
                    desc_short = desc_translated[:200] + "..." if len(desc_translated) > 200 else desc_translated
                    jira_info += f"• Description: {desc_short}\n"

                if fields.get('comments_text'):
                    jira_info += f"• Comments ({len(fields['comments_text'])}):\n"
                    for i, comment in enumerate(fields['comments_text'][:2]):
                        # Translate comments
                        comment_translated = translate_to_english(comment)
                        comment_short = comment_translated[:150] + "..." if len(comment_translated) > 150 else comment_translated
                        jira_info += f"  {i+1}. {comment_short}\n"

                jira_info += "\n"
            else:
                jira_info += f"**{jira_key}** - Task not found in JIRA\n\n"

        analysis['jira_info'] = jira_info
    
    # Bitbucket Analysis with enhanced business descriptions
    related_commits = find_related_commits(jira_keys, simon_comment, task_description, all_commits)

    if related_commits:
        bitbucket_info = "**BITBUCKET DEVELOPMENT ACTIVITY:**\n\n"

        for rel_commit in related_commits:
            commit = rel_commit['commit']
            commit_type = rel_commit['type']

            # Handle author field safely
            if 'author' in commit and commit['author']:
                if isinstance(commit['author'], dict):
                    author = commit['author'].get('display_name', commit['author'].get('raw', 'Unknown'))
                else:
                    author = str(commit['author'])
            else:
                author = 'Unknown'

            repo = commit.get('repository', 'Unknown').replace('trust-', '').title()
            date = commit.get('date', '')[:10]
            message = commit.get('message', '')

            # Get enhanced business description
            business_details = create_business_description_from_commit(message)
            if isinstance(business_details, dict):
                business_category = business_details['category']
                business_impact = business_details['impact']
                business_value = business_details['business_value']
            else:
                business_category = business_details
                business_impact = "Contributes to system development"
                business_value = "Supports project objectives"

            if commit_type == 'exact_jira':
                matching_jira = rel_commit.get('matching_jira', [])
                bitbucket_info += f"**{repo} Repository** ({date})\n"
                bitbucket_info += f"• **Business Activity:** {business_category}\n"
                bitbucket_info += f"• **Business Impact:** {business_impact}\n"
                bitbucket_info += f"• **Business Value:** {business_value}\n"
                bitbucket_info += f"• **Developer:** {author}\n"
                bitbucket_info += f"• **JIRA Tasks:** {', '.join(matching_jira)}\n"
                bitbucket_info += f"• **Technical Summary:** {message.split(chr(10))[0][:100]}...\n\n"
            else:
                score = rel_commit.get('score', 0)
                bitbucket_info += f"**{repo} Repository** ({date}) - Relevance Score: {score}\n"
                bitbucket_info += f"• **Business Activity:** {business_category}\n"
                bitbucket_info += f"• **Business Impact:** {business_impact}\n"
                bitbucket_info += f"• **Developer:** {author}\n"
                bitbucket_info += f"• **Technical Summary:** {message.split(chr(10))[0][:100]}...\n\n"

        analysis['bitbucket_info'] = bitbucket_info
    
    # Combined Analysis
    combined = "**COMPREHENSIVE ANALYSIS:**\n\n"
    combined += f"**Simon's Comment:** {simon_comment}\n\n"
    
    if jira_keys:
        combined += f"**Related JIRA Tasks:** {', '.join(jira_keys)}\n"
    
    if related_commits:
        exact_matches = [rc for rc in related_commits if rc['type'] == 'exact_jira']
        contextual_matches = [rc for rc in related_commits if rc['type'] == 'contextual']
        
        if exact_matches:
            combined += f"**Direct Development Activity:** {len(exact_matches)} commits found\n"
        if contextual_matches:
            combined += f"**Related Development Activity:** {len(contextual_matches)} potentially related commits\n"
    
    # Classify the issue type
    simon_lower = simon_comment.lower()
    if re.search(r'bug|error|issue|problem|fix|broken|not working|fail', simon_lower):
        issue_type = "🐛 Bug Report"
    elif re.search(r'improve|enhance|add|new|feature|change|better', simon_lower):
        issue_type = "✨ Enhancement Request"
    elif re.search(r'question|explain|clarify|understand|what|how|why', simon_lower):
        issue_type = "❓ Question"
    elif re.search(r'test|testing|verify|check|validation', simon_lower):
        issue_type = "🧪 Testing"
    else:
        issue_type = "📝 General Comment"
    
    combined += f"**Issue Classification:** {issue_type}\n\n"
    
    if analysis['jira_info']:
        combined += "**JIRA Integration:** ✅ Linked to project management\n"
    else:
        combined += "**JIRA Integration:** ❌ No direct task linkage\n"
    
    if analysis['bitbucket_info']:
        combined += "**Development Tracking:** ✅ Related code changes found\n"
    else:
        combined += "**Development Tracking:** ❌ No related development activity\n"
    
    analysis['combined_analysis'] = combined
    
    return analysis

def main():
    """Main function"""
    print("Creating comprehensive analysis for Simon's comments...")
    print("Integrating JIRA (RASEA + RS) and Bitbucket (Trust repositories)")
    
    # Load source file
    source_filename = "clockify_source_simon.xlsx"
    
    try:
        print(f"\n=== LOADING SOURCE FILE ===")
        df = pd.read_excel(source_filename, sheet_name="Detailed Report")
        print(f"Loaded {len(df)} rows from source file")
        
        # Find Simon column
        simon_column = None
        for col in df.columns:
            if 'simon' in str(col).lower():
                simon_column = col
                break
        
        if simon_column is None:
            print("Simon column not found")
            return
        
        print(f"Found Simon column: {simon_column}")
        
        # Count rows with Simon comments
        simon_rows = df[df[simon_column].notna() & (df[simon_column] != '') & (df[simon_column] != 'nan')]
        print(f"Found {len(simon_rows)} rows with Simon's comments")
        
        # Get all JIRA issues
        print(f"\n=== RETRIEVING JIRA DATA ===")
        all_jira_issues = get_all_jira_issues()
        print(f"Retrieved {len(all_jira_issues)} JIRA issues from both projects")
        
        # Get all Bitbucket commits
        print(f"\n=== RETRIEVING BITBUCKET DATA ===")
        all_commits = get_all_bitbucket_commits()
        print(f"Retrieved {len(all_commits)} commits from all repositories")
        
        # Analyze rows with Simon's comments
        print(f"\n=== ANALYZING SIMON'S COMMENTS ===")
        jira_info_responses = []
        bitbucket_info_responses = []
        combined_analysis_responses = []
        work_effort_responses = []

        for index, row in df.iterrows():
            analysis = analyze_simon_row(row, all_jira_issues, all_commits)

            if analysis:
                print(f"Processing row {index + 1}: {analysis['simon_comment'][:50]}...")
                jira_info_responses.append(analysis['jira_info'])
                bitbucket_info_responses.append(analysis['bitbucket_info'])
                combined_analysis_responses.append(analysis['combined_analysis'])

                # Calculate work effort
                effort_hours, complexity_factors = estimate_work_effort(
                    analysis['simon_comment'],
                    analysis['jira_info'],
                    analysis['bitbucket_info'],
                    str(row.get('Description', ''))
                )

                effort_description = f"{effort_hours} hours"
                if complexity_factors:
                    effort_description += f" (Factors: {', '.join(complexity_factors)})"

                work_effort_responses.append(effort_description)
            else:
                jira_info_responses.append("")
                bitbucket_info_responses.append("")
                combined_analysis_responses.append("")
                work_effort_responses.append("")
        
        # Translate all columns to English
        print(f"\n=== TRANSLATING TO ENGLISH ===")
        df_english = df.copy()
        
        # Translate column names
        column_translations = {
            'Projekt': 'Project',
            'Klient': 'Client',
            'Opis': 'Description',
            'Zadanie': 'Task',
            'Użytkownik': 'User',
            'Grupa': 'Group',
            'Tagi': 'Tags',
            'Płatne': 'Billable',
            'Data rozpoczęcia': 'Start Date',
            'Godzina rozpoczęcia': 'Start Time',
            'Data zakończenia': 'End Date',
            'Godzina zakończenia': 'End Time',
            'Czas trwania (h)': 'Duration (h)',
            'Czas trwania (dziesiętny)': 'Duration (decimal)',
            'Stawka płatna (USD)': 'Billable Rate (USD)',
            'Kwota płatna (USD)': 'Billable Amount (USD)',
            'Kategoria': 'Category'
        }
        
        df_english = df_english.rename(columns=column_translations)
        
        # Translate content in all columns
        for col in df_english.columns:
            if df_english[col].dtype == 'object':
                df_english[col] = df_english[col].apply(translate_to_english)
        
        # Add new analysis columns
        df_english['JIRA Information'] = jira_info_responses
        df_english['Bitbucket Development'] = bitbucket_info_responses
        df_english['Work Effort Estimate'] = work_effort_responses
        df_english['Comprehensive Analysis'] = combined_analysis_responses
        
        # Save enhanced file
        output_filename = f"Clockify_Complete_Simon_Analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        with pd.ExcelWriter(output_filename, engine='openpyxl') as writer:
            df_english.to_excel(writer, sheet_name='Complete Analysis', index=False)
            
            # Adjust column widths
            worksheet = writer.sheets['Complete Analysis']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                
                # Special widths for analysis columns
                if column[0].value in ['JIRA Information', 'Bitbucket Development', 'Comprehensive Analysis']:
                    adjusted_width = min(max_length + 2, 120)
                elif column[0].value == 'Work Effort Estimate':
                    adjusted_width = min(max_length + 2, 40)
                else:
                    adjusted_width = min(max_length + 2, 30)
                
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        print(f"\n✅ SUCCESS! Complete analysis created: {output_filename}")
        
        # Statistics
        jira_linked = len([r for r in jira_info_responses if r])
        bitbucket_linked = len([r for r in bitbucket_info_responses if r])
        total_simon_comments = len([r for r in combined_analysis_responses if r])
        
        print(f"\n📊 Analysis Statistics:")
        print(f"- Total Simon comments analyzed: {total_simon_comments}")
        print(f"- Comments with JIRA information: {jira_linked} ({jira_linked/total_simon_comments*100:.1f}%)")
        print(f"- Comments with Bitbucket activity: {bitbucket_linked} ({bitbucket_linked/total_simon_comments*100:.1f}%)")
        print(f"- JIRA issues available: {len(all_jira_issues)}")
        print(f"- Bitbucket commits available: {len(all_commits)}")
        
        return output_filename
        
    except Exception as e:
        print(f"Error processing file: {e}")
        return None

if __name__ == "__main__":
    main()
