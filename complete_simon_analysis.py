#!/usr/bin/env py
# -*- coding: utf-8 -*-

import pandas as pd
import requests
import json
import base64
import re
from datetime import datetime, timedelta, timezone
from collections import defaultdict

# JIRA Configuration
JIRA_URL = "https://read-at-sea.atlassian.net"
JIRA_EMAIL = "j.w<PERSON><PERSON><PERSON>@a-soft.pl"
JIRA_API_TOKEN = "ATATT3xFfGF0o1tVy4he6mvThd2U0mZU2NCqHbZoCB216R6aPWZZUnKecNaRaTIFIBkmb3sTHDvEPtjnJ__aW1EGolFwrIFPuo5qY9Z4SEW2TlMMt8d8v0sLWKkGGMkjLSDQQyP2wT4BImUn_XaNGozE1-KzMj1GFxhAtdkdmwtMErw0hkwPo4Q=D1D6CC05"

# Bitbucket Configuration
BITBUCKET_USERNAME = "mkasoft"
BITBUCKET_APP_PASSWORD = "ATBBspuHWSzDP9kUnNBRJWbLyGAP7E2DE5BA"
BITBUCKET_WORKSPACE = "a-softy"

# Repositories to analyze
REPOSITORIES = ["trust-netcore", "trust-react", "trust-azure-functions"]

def get_all_jira_issues():
    """Retrieves all issues from both JIRA projects (RASEA and RS)"""
    
    auth_string = f"{JIRA_EMAIL}:{JIRA_API_TOKEN}"
    auth_bytes = auth_string.encode('ascii')
    auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
    
    headers = {
        'Authorization': f'Basic {auth_b64}',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }
    
    all_issues = {}
    projects = ['RASEA', 'RS']
    
    for project in projects:
        print(f"Retrieving issues from JIRA project {project}...")
        
        url = f"{JIRA_URL}/rest/api/3/search"
        start_at = 0
        max_results = 50
        
        while True:
            params = {
                'jql': f'project = {project} ORDER BY created DESC',
                'startAt': start_at,
                'maxResults': max_results,
                'fields': 'key,summary,status,assignee,reporter,created,updated,priority,issuetype,description,components,labels,comment'
            }
            
            try:
                response = requests.get(url, headers=headers, params=params)
                response.raise_for_status()
                data = response.json()
                
                issues = data.get('issues', [])
                
                for issue in issues:
                    issue_key = issue['key']
                    fields = issue.get('fields', {})
                    
                    # Process description
                    if fields.get('description'):
                        fields['description_text'] = extract_text_from_jira_content(fields['description'])
                    
                    # Process comments
                    comments_text = []
                    if fields.get('comment', {}).get('comments'):
                        for comment in fields['comment']['comments']:
                            if comment.get('body'):
                                comment_text = extract_text_from_jira_content(comment['body'])
                                author = comment.get('author', {}).get('displayName', 'Unknown')
                                created = comment.get('created', '')
                                comments_text.append(f"[{author}, {created[:10]}]: {comment_text}")
                    
                    fields['comments_text'] = comments_text
                    fields['all_comments_combined'] = ' | '.join(comments_text)
                    
                    all_issues[issue_key] = fields
                
                total = data.get('total', 0)
                print(f"Retrieved {len(issues)} issues from {project}, total: {len([k for k in all_issues.keys() if k.startswith(project)])}/{total}")
                
                if start_at + len(issues) >= total or len(issues) == 0:
                    break
                    
                start_at += max_results
                
            except requests.exceptions.RequestException as e:
                print(f"Error retrieving data from JIRA project {project}: {e}")
                break
    
    return all_issues

def get_all_bitbucket_commits():
    """Retrieves all commits from Bitbucket repositories (last 6 months)"""
    
    auth_string = f"{BITBUCKET_USERNAME}:{BITBUCKET_APP_PASSWORD}"
    auth_bytes = auth_string.encode('ascii')
    auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
    
    headers = {
        'Authorization': f'Basic {auth_b64}',
        'Accept': 'application/json'
    }
    
    all_commits = []
    six_months_ago = datetime.now(timezone.utc) - timedelta(days=180)
    
    for repo in REPOSITORIES:
        print(f"Retrieving commits from {repo}...")
        
        url = f"https://api.bitbucket.org/2.0/repositories/{BITBUCKET_WORKSPACE}/{repo}/commits/master"
        
        while url:
            try:
                response = requests.get(url, headers=headers)
                response.raise_for_status()
                data = response.json()
                
                commits = data.get('values', [])
                
                for commit in commits:
                    commit_date = datetime.fromisoformat(commit['date'].replace('Z', '+00:00'))
                    
                    if commit_date < six_months_ago:
                        break  # Stop when we reach older commits
                    
                    commit['repository'] = repo
                    all_commits.append(commit)
                
                url = data.get('next')
                
            except requests.exceptions.RequestException as e:
                print(f"Error fetching commits from {repo}: {e}")
                break
    
    print(f"Retrieved {len(all_commits)} total commits from all repositories")
    return all_commits

def extract_text_from_jira_content(content):
    """Extracts text from JIRA content structure (JSON)"""
    if not content:
        return ""
    
    if isinstance(content, str):
        return content
    
    if isinstance(content, dict):
        text_parts = []
        
        if content.get('type') == 'doc' and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))
        elif content.get('type') == 'paragraph' and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))
        elif content.get('type') == 'text' and 'text' in content:
            text_parts.append(content['text'])
        elif content.get('type') in ['heading', 'codeBlock', 'blockquote'] and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))
        elif content.get('type') in ['bulletList', 'orderedList'] and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))
        elif content.get('type') == 'listItem' and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))
        
        return ' '.join(text_parts)
    
    elif isinstance(content, list):
        text_parts = []
        for item in content:
            text_parts.append(extract_text_from_jira_content(item))
        return ' '.join(text_parts)
    
    return str(content)

def extract_jira_keys(text):
    """Extracts JIRA keys from text"""
    if pd.isna(text) or not isinstance(text, str):
        return []
    
    patterns = [
        r'\b(RS-\d+)\b',
        r'\b(RASEA-\d+)\b',
        r'\b([A-Z]+-\d+)\b'
    ]
    
    jira_keys = []
    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        jira_keys.extend(matches)
    
    return list(set(jira_keys))

def find_related_commits(jira_keys, simon_comment, task_description, all_commits):
    """Finds commits related to JIRA keys or contextually related"""
    
    related_commits = []
    
    # First, find commits with exact JIRA matches
    for commit in all_commits:
        message = commit.get('message', '')
        commit_jira_keys = extract_jira_keys(message)
        
        # Check for exact JIRA matches
        if any(jira_key in commit_jira_keys for jira_key in jira_keys):
            related_commits.append({
                'type': 'exact_jira',
                'commit': commit,
                'matching_jira': [jk for jk in jira_keys if jk in commit_jira_keys]
            })
    
    # If no exact matches, try contextual matching
    if not related_commits and simon_comment:
        keywords = extract_keywords(simon_comment + " " + task_description)
        
        for commit in all_commits:
            message = commit.get('message', '').lower()
            score = sum(1 for keyword in keywords if keyword in message)
            
            if score > 0:
                related_commits.append({
                    'type': 'contextual',
                    'commit': commit,
                    'score': score,
                    'keywords': keywords
                })
        
        # Sort contextual matches by score
        related_commits = sorted(related_commits, key=lambda x: x.get('score', 0), reverse=True)[:3]
    
    return related_commits

def extract_keywords(text):
    """Extracts meaningful keywords from text"""
    if not text:
        return []
    
    words = re.findall(r'\b\w+\b', text.lower())
    common_words = {'this', 'that', 'with', 'from', 'they', 'have', 'been', 'were', 'said', 'each', 'which', 'their', 'time', 'will', 'about', 'would', 'there', 'could', 'other', 'more', 'very', 'what', 'know', 'just', 'first', 'into', 'over', 'think', 'also', 'your', 'work', 'life', 'only', 'can', 'still', 'should', 'after', 'being', 'now', 'made', 'before', 'here', 'through', 'when', 'where', 'much', 'some', 'these', 'many', 'then', 'them', 'well'}
    
    return [word for word in words if len(word) > 3 and word not in common_words]

def create_business_description_from_commit(commit_message):
    """Creates business-friendly description from commit message"""
    
    message_lower = commit_message.lower()
    
    patterns = {
        r'fix|bug|error|issue|hotfix': 'System Issue Resolution',
        r'add|new|create|implement|feature': 'New Feature Development',
        r'update|modify|change|improve|enhance': 'System Enhancement',
        r'remove|delete|clean': 'Code Optimization',
        r'refactor|restructure': 'Code Quality Improvement',
        r'test|spec|unit|validation': 'Quality Assurance',
        r'config|setup|deploy|release': 'System Deployment',
        r'ui|interface|frontend|react': 'User Interface Enhancement',
        r'api|backend|service|netcore': 'Backend Service Enhancement',
        r'database|db|sql|data': 'Data Management Enhancement',
        r'security|auth|login': 'Security Enhancement',
        r'performance|optimize|speed': 'Performance Optimization',
        r'merge|pull request|pr': 'Code Integration',
        r'cargo|vessel|ship': 'Maritime Operations Enhancement',
        r'emission|environmental': 'Environmental Compliance',
        r'meter|reading|measurement': 'Monitoring System Enhancement'
    }
    
    for pattern, description in patterns.items():
        if re.search(pattern, message_lower):
            return description
    
    return "Development Activity"

def translate_to_english(text):
    """Translates Polish terms to English"""
    if pd.isna(text) or not isinstance(text, str):
        return text
    
    translations = {
        'Projekt': 'Project',
        'Klient': 'Client',
        'Opis': 'Description',
        'Zadanie': 'Task',
        'Użytkownik': 'User',
        'Grupa': 'Group',
        'Tagi': 'Tags',
        'Płatne': 'Billable',
        'Data rozpoczęcia': 'Start Date',
        'Godzina rozpoczęcia': 'Start Time',
        'Data zakończenia': 'End Date',
        'Godzina zakończenia': 'End Time',
        'Czas trwania (h)': 'Duration (h)',
        'Czas trwania (dziesiętny)': 'Duration (decimal)',
        'Stawka płatna (USD)': 'Billable Rate (USD)',
        'Kwota płatna (USD)': 'Billable Amount (USD)',
        'Kategoria': 'Category',
        'Bug': 'Bug',
        'Zadanie': 'Task',
        'Usprawnienie': 'Improvement',
        'Pytanie': 'Question',
        'Test': 'Test',
        'Tak': 'Yes',
        'Nie': 'No',
        'ReadAtSea': 'ReadAtSea',
        'Trust': 'Trust'
    }
    
    result = str(text)
    for polish, english in translations.items():
        result = result.replace(polish, english)
    
    return result

def analyze_simon_row(row, all_jira_issues, all_commits):
    """Analyzes a row where Simon added a comment"""
    
    simon_comment = str(row.get('Simon', ''))
    if pd.isna(simon_comment) or simon_comment.strip() == '' or simon_comment == 'nan':
        return None
    
    # Get row data
    task_description = str(row.get('Description', ''))
    task_name = str(row.get('Task', ''))
    
    # Extract JIRA keys from all row data
    all_row_text = f"{simon_comment} {task_description} {task_name}"
    for col, value in row.items():
        if pd.notna(value) and isinstance(value, str):
            all_row_text += f" {value}"
    
    jira_keys = extract_jira_keys(all_row_text)
    
    # Prepare analysis
    analysis = {
        'simon_comment': simon_comment,
        'jira_info': '',
        'bitbucket_info': '',
        'combined_analysis': ''
    }
    
    # JIRA Analysis
    if jira_keys:
        jira_info = "**JIRA TASK INFORMATION:**\n\n"
        
        for jira_key in jira_keys:
            if jira_key in all_jira_issues:
                fields = all_jira_issues[jira_key]
                project = "RASEA" if jira_key.startswith("RASEA") else "RS"
                
                jira_info += f"**{jira_key}** (Project: {project})\n"
                jira_info += f"• Title: {fields.get('summary', 'No title')}\n"
                jira_info += f"• Status: {fields.get('status', {}).get('name', 'Unknown')}\n"
                jira_info += f"• Type: {fields.get('issuetype', {}).get('name', 'Unknown')}\n"
                jira_info += f"• Priority: {fields.get('priority', {}).get('name', 'Unknown')}\n"
                
                if fields.get('assignee'):
                    jira_info += f"• Assigned to: {fields['assignee'].get('displayName', 'Unknown')}\n"
                
                if fields.get('description_text'):
                    desc_short = fields['description_text'][:200] + "..." if len(fields['description_text']) > 200 else fields['description_text']
                    jira_info += f"• Description: {desc_short}\n"
                
                if fields.get('comments_text'):
                    jira_info += f"• Comments ({len(fields['comments_text'])}):\n"
                    for i, comment in enumerate(fields['comments_text'][:2]):
                        comment_short = comment[:150] + "..." if len(comment) > 150 else comment
                        jira_info += f"  {i+1}. {comment_short}\n"
                
                jira_info += "\n"
            else:
                jira_info += f"**{jira_key}** - Task not found in JIRA\n\n"
        
        analysis['jira_info'] = jira_info
    
    # Bitbucket Analysis
    related_commits = find_related_commits(jira_keys, simon_comment, task_description, all_commits)
    
    if related_commits:
        bitbucket_info = "**BITBUCKET DEVELOPMENT ACTIVITY:**\n\n"
        
        for rel_commit in related_commits:
            commit = rel_commit['commit']
            commit_type = rel_commit['type']
            
            # Handle author field safely
            if 'author' in commit and commit['author']:
                if isinstance(commit['author'], dict):
                    author = commit['author'].get('display_name', commit['author'].get('raw', 'Unknown'))
                else:
                    author = str(commit['author'])
            else:
                author = 'Unknown'
            
            repo = commit.get('repository', 'Unknown')
            date = commit.get('date', '')[:10]
            message = commit.get('message', '')
            
            business_desc = create_business_description_from_commit(message)
            
            if commit_type == 'exact_jira':
                matching_jira = rel_commit.get('matching_jira', [])
                bitbucket_info += f"**{repo}** ({date}) - {business_desc}\n"
                bitbucket_info += f"• Developer: {author}\n"
                bitbucket_info += f"• JIRA Tasks: {', '.join(matching_jira)}\n"
                bitbucket_info += f"• Technical Details: {message.split(chr(10))[0][:100]}...\n\n"
            else:
                score = rel_commit.get('score', 0)
                bitbucket_info += f"**{repo}** ({date}) - {business_desc} (Relevance: {score})\n"
                bitbucket_info += f"• Developer: {author}\n"
                bitbucket_info += f"• Technical Details: {message.split(chr(10))[0][:100]}...\n\n"
        
        analysis['bitbucket_info'] = bitbucket_info
    
    # Combined Analysis
    combined = "**COMPREHENSIVE ANALYSIS:**\n\n"
    combined += f"**Simon's Comment:** {simon_comment}\n\n"
    
    if jira_keys:
        combined += f"**Related JIRA Tasks:** {', '.join(jira_keys)}\n"
    
    if related_commits:
        exact_matches = [rc for rc in related_commits if rc['type'] == 'exact_jira']
        contextual_matches = [rc for rc in related_commits if rc['type'] == 'contextual']
        
        if exact_matches:
            combined += f"**Direct Development Activity:** {len(exact_matches)} commits found\n"
        if contextual_matches:
            combined += f"**Related Development Activity:** {len(contextual_matches)} potentially related commits\n"
    
    # Classify the issue type
    simon_lower = simon_comment.lower()
    if re.search(r'bug|error|issue|problem|fix|broken|not working|fail', simon_lower):
        issue_type = "🐛 Bug Report"
    elif re.search(r'improve|enhance|add|new|feature|change|better', simon_lower):
        issue_type = "✨ Enhancement Request"
    elif re.search(r'question|explain|clarify|understand|what|how|why', simon_lower):
        issue_type = "❓ Question"
    elif re.search(r'test|testing|verify|check|validation', simon_lower):
        issue_type = "🧪 Testing"
    else:
        issue_type = "📝 General Comment"
    
    combined += f"**Issue Classification:** {issue_type}\n\n"
    
    if analysis['jira_info']:
        combined += "**JIRA Integration:** ✅ Linked to project management\n"
    else:
        combined += "**JIRA Integration:** ❌ No direct task linkage\n"
    
    if analysis['bitbucket_info']:
        combined += "**Development Tracking:** ✅ Related code changes found\n"
    else:
        combined += "**Development Tracking:** ❌ No related development activity\n"
    
    analysis['combined_analysis'] = combined
    
    return analysis

def main():
    """Main function"""
    print("Creating comprehensive analysis for Simon's comments...")
    print("Integrating JIRA (RASEA + RS) and Bitbucket (Trust repositories)")
    
    # Load source file
    source_filename = "clockify_source_simon.xlsx"
    
    try:
        print(f"\n=== LOADING SOURCE FILE ===")
        df = pd.read_excel(source_filename, sheet_name="Detailed Report")
        print(f"Loaded {len(df)} rows from source file")
        
        # Find Simon column
        simon_column = None
        for col in df.columns:
            if 'simon' in str(col).lower():
                simon_column = col
                break
        
        if simon_column is None:
            print("Simon column not found")
            return
        
        print(f"Found Simon column: {simon_column}")
        
        # Count rows with Simon comments
        simon_rows = df[df[simon_column].notna() & (df[simon_column] != '') & (df[simon_column] != 'nan')]
        print(f"Found {len(simon_rows)} rows with Simon's comments")
        
        # Get all JIRA issues
        print(f"\n=== RETRIEVING JIRA DATA ===")
        all_jira_issues = get_all_jira_issues()
        print(f"Retrieved {len(all_jira_issues)} JIRA issues from both projects")
        
        # Get all Bitbucket commits
        print(f"\n=== RETRIEVING BITBUCKET DATA ===")
        all_commits = get_all_bitbucket_commits()
        print(f"Retrieved {len(all_commits)} commits from all repositories")
        
        # Analyze rows with Simon's comments
        print(f"\n=== ANALYZING SIMON'S COMMENTS ===")
        jira_info_responses = []
        bitbucket_info_responses = []
        combined_analysis_responses = []
        
        for index, row in df.iterrows():
            analysis = analyze_simon_row(row, all_jira_issues, all_commits)
            
            if analysis:
                print(f"Processing row {index + 1}: {analysis['simon_comment'][:50]}...")
                jira_info_responses.append(analysis['jira_info'])
                bitbucket_info_responses.append(analysis['bitbucket_info'])
                combined_analysis_responses.append(analysis['combined_analysis'])
            else:
                jira_info_responses.append("")
                bitbucket_info_responses.append("")
                combined_analysis_responses.append("")
        
        # Translate all columns to English
        print(f"\n=== TRANSLATING TO ENGLISH ===")
        df_english = df.copy()
        
        # Translate column names
        column_translations = {
            'Projekt': 'Project',
            'Klient': 'Client',
            'Opis': 'Description',
            'Zadanie': 'Task',
            'Użytkownik': 'User',
            'Grupa': 'Group',
            'Tagi': 'Tags',
            'Płatne': 'Billable',
            'Data rozpoczęcia': 'Start Date',
            'Godzina rozpoczęcia': 'Start Time',
            'Data zakończenia': 'End Date',
            'Godzina zakończenia': 'End Time',
            'Czas trwania (h)': 'Duration (h)',
            'Czas trwania (dziesiętny)': 'Duration (decimal)',
            'Stawka płatna (USD)': 'Billable Rate (USD)',
            'Kwota płatna (USD)': 'Billable Amount (USD)',
            'Kategoria': 'Category'
        }
        
        df_english = df_english.rename(columns=column_translations)
        
        # Translate content in all columns
        for col in df_english.columns:
            if df_english[col].dtype == 'object':
                df_english[col] = df_english[col].apply(translate_to_english)
        
        # Add new analysis columns
        df_english['JIRA Information'] = jira_info_responses
        df_english['Bitbucket Development'] = bitbucket_info_responses
        df_english['Comprehensive Analysis'] = combined_analysis_responses
        
        # Save enhanced file
        output_filename = f"Clockify_Complete_Simon_Analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        with pd.ExcelWriter(output_filename, engine='openpyxl') as writer:
            df_english.to_excel(writer, sheet_name='Complete Analysis', index=False)
            
            # Adjust column widths
            worksheet = writer.sheets['Complete Analysis']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                
                # Special widths for analysis columns
                if column[0].value in ['JIRA Information', 'Bitbucket Development', 'Comprehensive Analysis']:
                    adjusted_width = min(max_length + 2, 120)
                else:
                    adjusted_width = min(max_length + 2, 30)
                
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        print(f"\n✅ SUCCESS! Complete analysis created: {output_filename}")
        
        # Statistics
        jira_linked = len([r for r in jira_info_responses if r])
        bitbucket_linked = len([r for r in bitbucket_info_responses if r])
        total_simon_comments = len([r for r in combined_analysis_responses if r])
        
        print(f"\n📊 Analysis Statistics:")
        print(f"- Total Simon comments analyzed: {total_simon_comments}")
        print(f"- Comments with JIRA information: {jira_linked} ({jira_linked/total_simon_comments*100:.1f}%)")
        print(f"- Comments with Bitbucket activity: {bitbucket_linked} ({bitbucket_linked/total_simon_comments*100:.1f}%)")
        print(f"- JIRA issues available: {len(all_jira_issues)}")
        print(f"- Bitbucket commits available: {len(all_commits)}")
        
        return output_filename
        
    except Exception as e:
        print(f"Error processing file: {e}")
        return None

if __name__ == "__main__":
    main()
