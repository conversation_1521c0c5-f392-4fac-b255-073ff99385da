{"values": [{"type": "commit", "hash": "0704d9408e5ede8388ec91f138231c2810e24f9f", "date": "2025-05-05T12:12:46+00:00", "author": {"type": "author", "raw": "<PERSON><PERSON><PERSON> <<EMAIL>>", "user": {"display_name": "<PERSON><PERSON><PERSON>", "links": {"self": {"href": "https://api.bitbucket.org/2.0/users/%7Bf748d839-e9f0-4bb7-a053-213de3072322%7D"}, "avatar": {"href": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/6208fd29682913007083cb49/359be245-89f5-4741-a517-b57e2aa456a8/128"}, "html": {"href": "https://bitbucket.org/%7Bf748d839-e9f0-4bb7-a053-213de3072322%7D/"}}, "type": "user", "uuid": "{f748d839-e9f0-4bb7-a053-213de3072322}", "account_id": "6208fd29682913007083cb49", "nickname": "<PERSON><PERSON><PERSON>"}}, "message": "Merged in release/24.04-v0.9 (pull request #510)\n\nRelease/24.04 v0.9\n\n* Merge branch 'feature/RASEA-886' into package/07.04-v0.9\n\n* Merged in package/07.04-v0.9 (pull request #506)\n\nPackage/07.04 v0.9\n\n* Add validation to user form\n\n* Add validation to role form\n\n* Add validation to cargo form\n\n* Fix misleading placeholder\n\n* Add validation to meter form\n\n* Add validation to formula form\n\n* Add validation to fuel form\n\n* Fix permissions for report tabs\n\n* Add loading to role modal\n\n* Remove ocr for menu\n\n* Update bunkerin note page\n\n* Merge branch 'feature/RASEA-879' into package/07.04-v0.9\n\n* Merge branch 'feature/RASEA-873' into package/07.04-v0.9\n\n* Merge branch 'feature/RASEA-881' into package/07.04-v0.9\n\n* Merge branch 'feature/RASEA-886' into package/07.04-v0.9\n\n* Fix for empty date in save storage\n\n* Fix for formula form\n\n* Merge branch 'feature/RASEA-879' of https://bitbucket.org/a-softy/trust-react into feature/RASEA-879\n\n* Fix for user form\n\n* Fix error message\n\n* Merge branches 'feature/RASEA-879' and 'feature/RASEA-879' of https://bitbucket.org/a-softy/trust-react into feature/RASEA-879\n\n* Fix scrubber input\n\n* Merge branch 'feature/RASEA-879' into package/07.04-v0.9\n\n* Merge branch 'package/07.04-v0.9' into develop\n\n# Conflicts:\n#\tsrc/scenes/ConsumptionFormulae/index.tsx\n#\tsrc/scenes/Users/<USER>/createOrUpdateUser.tsx\n#\tsrc/scenes/Users/<USER>/createOrUpdateUser.validation.ts\n\n* Merged in package/25.04-v0.9 (pull request #507)\n\nPackage/25.04 v0.9\n\n* Update login page\n\n* Update ocr in menu\n\n* Make tabs in menu links\n\n* Update menu\n\n* Remove seconds from cargo filter\n\n* Fix sorting in performance report for Nan values\n\n* Add fuel type column to consumption result\n\n* Remove empty filter options\n\n* Display only status if user can not edit readings\n\n* Add download and toggle status buttons\n\n* Add modal after statuses\n\n* Update headers and order of columns\n\n* Update and fix permissions\n\n* Update responsivness of performance report\n\n* Remove margin\n\n* Responsivess of pefromance report\n\n* Merge branch 'RASEA-901' of https://bitbucket.org/a-softy/trust-react into RASEA-901\n\n* Merge branch 'feature/RASEA-876' into package/25.04-v0.9\n\n* Merge branch 'feature/RASEA-887' into package/25.04-v0.9\n\n* Merge branch 'feature/RASEA-890' into package/25.04-v0.9\n\n* Merge branch 'feature/RASEA-893' into package/25.04-v0.9\n\n# Conflicts:\n#\tsrc/scenes/Roles/components/createOrUpdateRole.tsx\n\n* Merge branch 'f…\n* Fix styling\n\n* Update degree symbol\n\n* Update login page, for better UX\n\n* Hidden seconds from date\n\n* Fixed utc date\n\n* Added utc to string\n\n* Merge branch 'feature/RASEA-903' into package/05.05-v0.9\n\n# Conflicts:\n#\tsrc/scenes/Login/index.tsx\n\n* Merge branch 'feature/RASEA-906' into package/05.05-v0.9\n\n* Merged in package/05.05-v0.9 (pull request #508)\n\nPackage/05.05 v0.9\n\n* Merge branch 'RASEA-910-Edycja-daty-w-ekranie-cargo' into package/05.05-v0.9\n\n* Merged in package/05.05-v0.9 (pull request #509)\n\nPackage/05.05 v0.9\n\n* Revert \"Merged in package/05.05-v0.9 (pull request #509)\"\n\nThis reverts commit c988cfb92808a0e1e6161f6c724da8db20489825, reversing\nchanges made to 2bbca4cfa98bfc323a9b621826b0ac3bebd66b70.\n\n* Merge branch 'master' into release/24.04-v0.9\n", "summary": {"type": "rendered", "raw": "Merged in release/24.04-v0.9 (pull request #510)\n\nRelease/24.04 v0.9\n\n* Merge branch 'feature/RASEA-886' into package/07.04-v0.9\n\n* Merged in package/07.04-v0.9 (pull request #506)\n\nPackage/07.04 v0.9\n\n* Add validation to user form\n\n* Add validation to role form\n\n* Add validation to cargo form\n\n* Fix misleading placeholder\n\n* Add validation to meter form\n\n* Add validation to formula form\n\n* Add validation to fuel form\n\n* Fix permissions for report tabs\n\n* Add loading to role modal\n\n* Remove ocr for menu\n\n* Update bunkerin note page\n\n* Merge branch 'feature/RASEA-879' into package/07.04-v0.9\n\n* Merge branch 'feature/RASEA-873' into package/07.04-v0.9\n\n* Merge branch 'feature/RASEA-881' into package/07.04-v0.9\n\n* Merge branch 'feature/RASEA-886' into package/07.04-v0.9\n\n* Fix for empty date in save storage\n\n* Fix for formula form\n\n* Merge branch 'feature/RASEA-879' of https://bitbucket.org/a-softy/trust-react into feature/RASEA-879\n\n* Fix for user form\n\n* Fix error message\n\n* Merge branches 'feature/RASEA-879' and 'feature/RASEA-879' of https://bitbucket.org/a-softy/trust-react into feature/RASEA-879\n\n* Fix scrubber input\n\n* Merge branch 'feature/RASEA-879' into package/07.04-v0.9\n\n* Merge branch 'package/07.04-v0.9' into develop\n\n# Conflicts:\n#\tsrc/scenes/ConsumptionFormulae/index.tsx\n#\tsrc/scenes/Users/<USER>/createOrUpdateUser.tsx\n#\tsrc/scenes/Users/<USER>/createOrUpdateUser.validation.ts\n\n* Merged in package/25.04-v0.9 (pull request #507)\n\nPackage/25.04 v0.9\n\n* Update login page\n\n* Update ocr in menu\n\n* Make tabs in menu links\n\n* Update menu\n\n* Remove seconds from cargo filter\n\n* Fix sorting in performance report for Nan values\n\n* Add fuel type column to consumption result\n\n* Remove empty filter options\n\n* Display only status if user can not edit readings\n\n* Add download and toggle status buttons\n\n* Add modal after statuses\n\n* Update headers and order of columns\n\n* Update and fix permissions\n\n* Update responsivness of performance report\n\n* Remove margin\n\n* Responsivess of pefromance report\n\n* Merge branch 'RASEA-901' of https://bitbucket.org/a-softy/trust-react into RASEA-901\n\n* Merge branch 'feature/RASEA-876' into package/25.04-v0.9\n\n* Merge branch 'feature/RASEA-887' into package/25.04-v0.9\n\n* Merge branch 'feature/RASEA-890' into package/25.04-v0.9\n\n* Merge branch 'feature/RASEA-893' into package/25.04-v0.9\n\n# Conflicts:\n#\tsrc/scenes/Roles/components/createOrUpdateRole.tsx\n\n* Merge branch 'f…\n* Fix styling\n\n* Update degree symbol\n\n* Update login page, for better UX\n\n* Hidden seconds from date\n\n* Fixed utc date\n\n* Added utc to string\n\n* Merge branch 'feature/RASEA-903' into package/05.05-v0.9\n\n# Conflicts:\n#\tsrc/scenes/Login/index.tsx\n\n* Merge branch 'feature/RASEA-906' into package/05.05-v0.9\n\n* Merged in package/05.05-v0.9 (pull request #508)\n\nPackage/05.05 v0.9\n\n* Merge branch 'RASEA-910-Edycja-daty-w-ekranie-cargo' into package/05.05-v0.9\n\n* Merged in package/05.05-v0.9 (pull request #509)\n\nPackage/05.05 v0.9\n\n* Revert \"Merged in package/05.05-v0.9 (pull request #509)\"\n\nThis reverts commit c988cfb92808a0e1e6161f6c724da8db20489825, reversing\nchanges made to 2bbca4cfa98bfc323a9b621826b0ac3bebd66b70.\n\n* Merge branch 'master' into release/24.04-v0.9\n", "markup": "markdown", "html": "<p>Merged in release/24.04-v0.9 (<a href=\"https://bitbucket.org/a-softy/trust-react/pull-requests/510\" rel=\"nofollow\" class=\"ap-connect-link\">pull request #510</a>)</p>\n<p>Release/24.04 v0.9</p>\n<ul>\n<li>\n<p>Merge branch 'feature/RASEA-886' into package/07.04-v0.9</p>\n</li>\n<li>\n<p>Merged in package/07.04-v0.9 (<a href=\"https://bitbucket.org/a-softy/trust-react/pull-requests/506\" rel=\"nofollow\" class=\"ap-connect-link\">pull request #506</a>)</p>\n</li>\n</ul>\n<p>Package/07.04 v0.9</p>\n<ul>\n<li>\n<p>Add validation to user form</p>\n</li>\n<li>\n<p>Add validation to role form</p>\n</li>\n<li>\n<p>Add validation to cargo form</p>\n</li>\n<li>\n<p>Fix misleading placeholder</p>\n</li>\n<li>\n<p>Add validation to meter form</p>\n</li>\n<li>\n<p>Add validation to formula form</p>\n</li>\n<li>\n<p>Add validation to fuel form</p>\n</li>\n<li>\n<p>Fix permissions for report tabs</p>\n</li>\n<li>\n<p>Add loading to role modal</p>\n</li>\n<li>\n<p>Remove ocr for menu</p>\n</li>\n<li>\n<p>Update bunkerin note page</p>\n</li>\n<li>\n<p>Merge branch 'feature/RASEA-879' into package/07.04-v0.9</p>\n</li>\n<li>\n<p>Merge branch 'feature/RASEA-873' into package/07.04-v0.9</p>\n</li>\n<li>\n<p>Merge branch 'feature/RASEA-881' into package/07.04-v0.9</p>\n</li>\n<li>\n<p>Merge branch 'feature/RASEA-886' into package/07.04-v0.9</p>\n</li>\n<li>\n<p>Fix for empty date in save storage</p>\n</li>\n<li>\n<p>Fix for formula form</p>\n</li>\n<li>\n<p>Merge branch 'feature/RASEA-879' of <a href=\"https://bitbucket.org/a-softy/trust-react\" rel=\"nofollow\" class=\"ap-connect-link\">https://bitbucket.org/a-softy/trust-react</a> into feature/RASEA-879</p>\n</li>\n<li>\n<p>Fix for user form</p>\n</li>\n<li>\n<p>Fix error message</p>\n</li>\n<li>\n<p>Merge branches 'feature/RASEA-879' and 'feature/RASEA-879' of <a href=\"https://bitbucket.org/a-softy/trust-react\" rel=\"nofollow\" class=\"ap-connect-link\">https://bitbucket.org/a-softy/trust-react</a> into feature/RASEA-879</p>\n</li>\n<li>\n<p>Fix scrubber input</p>\n</li>\n<li>\n<p>Merge branch 'feature/RASEA-879' into package/07.04-v0.9</p>\n</li>\n<li>\n<p>Merge branch 'package/07.04-v0.9' into develop</p>\n</li>\n</ul>\n<h1 id=\"markdown-header-conflicts\">Conflicts:</h1>\n<h1 id=\"markdown-header-srcscenesconsumptionformulaeindextsx\">src/scenes/ConsumptionFormulae/index.tsx</h1>\n<h1 id=\"markdown-header-srcscenesuserscomponentscreateorupdateusertsx\">src/scenes/Users/<USER>/createOrUpdateUser.tsx</h1>\n<h1 id=\"markdown-header-srcscenesuserscomponentscreateorupdateuservalidationts\">src/scenes/Users/<USER>/createOrUpdateUser.validation.ts</h1>\n<ul>\n<li>Merged in package/25.04-v0.9 (<a href=\"https://bitbucket.org/a-softy/trust-react/pull-requests/507\" rel=\"nofollow\" class=\"ap-connect-link\">pull request #507</a>)</li>\n</ul>\n<p>Package/25.04 v0.9</p>\n<ul>\n<li>\n<p>Update login page</p>\n</li>\n<li>\n<p>Update ocr in menu</p>\n</li>\n<li>\n<p>Make tabs in menu links</p>\n</li>\n<li>\n<p>Update menu</p>\n</li>\n<li>\n<p>Remove seconds from cargo filter</p>\n</li>\n<li>\n<p>Fix sorting in performance report for Nan values</p>\n</li>\n<li>\n<p>Add fuel type column to consumption result</p>\n</li>\n<li>\n<p>Remove empty filter options</p>\n</li>\n<li>\n<p>Display only status if user can not edit readings</p>\n</li>\n<li>\n<p>Add download and toggle status buttons</p>\n</li>\n<li>\n<p>Add modal after statuses</p>\n</li>\n<li>\n<p>Update headers and order of columns</p>\n</li>\n<li>\n<p>Update and fix permissions</p>\n</li>\n<li>\n<p>Update responsivness of performance report</p>\n</li>\n<li>\n<p>Remove margin</p>\n</li>\n<li>\n<p>Responsivess of pefromance report</p>\n</li>\n<li>\n<p>Merge branch 'RASEA-901' of <a href=\"https://bitbucket.org/a-softy/trust-react\" rel=\"nofollow\" class=\"ap-connect-link\">https://bitbucket.org/a-softy/trust-react</a> into RASEA-901</p>\n</li>\n<li>\n<p>Merge branch 'feature/RASEA-876' into package/25.04-v0.9</p>\n</li>\n<li>\n<p>Merge branch 'feature/RASEA-887' into package/25.04-v0.9</p>\n</li>\n<li>\n<p>Merge branch 'feature/RASEA-890' into package/25.04-v0.9</p>\n</li>\n<li>\n<p>Merge branch 'feature/RASEA-893' into package/25.04-v0.9</p>\n</li>\n</ul>\n<h1 id=\"markdown-header-conflicts_1\">Conflicts:</h1>\n<h1 id=\"markdown-header-srcscenesrolescomponentscreateorupdateroletsx\">src/scenes/Roles/components/createOrUpdateRole.tsx</h1>\n<ul>\n<li>Merge branch 'f…</li>\n<li>\n<p>Fix styling</p>\n</li>\n<li>\n<p>Update degree symbol</p>\n</li>\n<li>\n<p>Update login page, for better UX</p>\n</li>\n<li>\n<p>Hidden seconds from date</p>\n</li>\n<li>\n<p>Fixed utc date</p>\n</li>\n<li>\n<p>Added utc to string</p>\n</li>\n<li>\n<p>Merge branch 'feature/RASEA-903' into package/05.05-v0.9</p>\n</li>\n</ul>\n<h1 id=\"markdown-header-conflicts_2\">Conflicts:</h1>\n<h1 id=\"markdown-header-srcscenesloginindextsx\">src/scenes/Login/index.tsx</h1>\n<ul>\n<li>\n<p>Merge branch 'feature/RASEA-906' into package/05.05-v0.9</p>\n</li>\n<li>\n<p>Merged in package/05.05-v0.9 (<a href=\"https://bitbucket.org/a-softy/trust-react/pull-requests/508\" rel=\"nofollow\" class=\"ap-connect-link\">pull request #508</a>)</p>\n</li>\n</ul>\n<p>Package/05.05 v0.9</p>\n<ul>\n<li>\n<p>Merge branch 'RASEA-910-Edycja-daty-w-ekranie-cargo' into package/05.05-v0.9</p>\n</li>\n<li>\n<p>Merged in package/05.05-v0.9 (<a href=\"https://bitbucket.org/a-softy/trust-react/pull-requests/509\" rel=\"nofollow\" class=\"ap-connect-link\">pull request #509</a>)</p>\n</li>\n</ul>\n<p>Package/05.05 v0.9</p>\n<ul>\n<li>Revert \"Merged in package/05.05-v0.9 (<a href=\"https://bitbucket.org/a-softy/trust-react/pull-requests/509\" rel=\"nofollow\" class=\"ap-connect-link\">pull request #509</a>)\"</li>\n</ul>\n<p>This reverts commit <a href=\"https://bitbucket.org/a-softy/trust-react/commits/c988cfb92808a0e1e6161f6c724da8db20489825\" rel=\"nofollow\" class=\"ap-connect-link\">c988cfb92808a0e1e6161f6c724da8db20489825</a>, reversing<br>\nchanges made to <a href=\"https://bitbucket.org/a-softy/trust-react/commits/2bbca4cfa98bfc323a9b621826b0ac3bebd66b70\" rel=\"nofollow\" class=\"ap-connect-link\">2bbca4cfa98bfc323a9b621826b0ac3bebd66b70</a>.</p>\n<ul>\n<li>Merge branch 'master' into release/24.04-v0.9</li>\n</ul>"}, "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/0704d9408e5ede8388ec91f138231c2810e24f9f"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react/commits/0704d9408e5ede8388ec91f138231c2810e24f9f"}, "diff": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/diff/0704d9408e5ede8388ec91f138231c2810e24f9f"}, "approve": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/0704d9408e5ede8388ec91f138231c2810e24f9f/approve"}, "comments": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/0704d9408e5ede8388ec91f138231c2810e24f9f/comments"}, "statuses": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/0704d9408e5ede8388ec91f138231c2810e24f9f/statuses"}, "patch": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/patch/0704d9408e5ede8388ec91f138231c2810e24f9f"}}, "parents": [{"hash": "ac4d1827eca96a22fdc0ce79fc68179d6dfc09a5", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/ac4d1827eca96a22fdc0ce79fc68179d6dfc09a5"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react/commits/ac4d1827eca96a22fdc0ce79fc68179d6dfc09a5"}}, "type": "commit"}], "rendered": {"message": {"type": "rendered", "raw": "Merged in release/24.04-v0.9 (pull request #510)\n\nRelease/24.04 v0.9\n\n* Merge branch 'feature/RASEA-886' into package/07.04-v0.9\n\n* Merged in package/07.04-v0.9 (pull request #506)\n\nPackage/07.04 v0.9\n\n* Add validation to user form\n\n* Add validation to role form\n\n* Add validation to cargo form\n\n* Fix misleading placeholder\n\n* Add validation to meter form\n\n* Add validation to formula form\n\n* Add validation to fuel form\n\n* Fix permissions for report tabs\n\n* Add loading to role modal\n\n* Remove ocr for menu\n\n* Update bunkerin note page\n\n* Merge branch 'feature/RASEA-879' into package/07.04-v0.9\n\n* Merge branch 'feature/RASEA-873' into package/07.04-v0.9\n\n* Merge branch 'feature/RASEA-881' into package/07.04-v0.9\n\n* Merge branch 'feature/RASEA-886' into package/07.04-v0.9\n\n* Fix for empty date in save storage\n\n* Fix for formula form\n\n* Merge branch 'feature/RASEA-879' of https://bitbucket.org/a-softy/trust-react into feature/RASEA-879\n\n* Fix for user form\n\n* Fix error message\n\n* Merge branches 'feature/RASEA-879' and 'feature/RASEA-879' of https://bitbucket.org/a-softy/trust-react into feature/RASEA-879\n\n* Fix scrubber input\n\n* Merge branch 'feature/RASEA-879' into package/07.04-v0.9\n\n* Merge branch 'package/07.04-v0.9' into develop\n\n# Conflicts:\n#\tsrc/scenes/ConsumptionFormulae/index.tsx\n#\tsrc/scenes/Users/<USER>/createOrUpdateUser.tsx\n#\tsrc/scenes/Users/<USER>/createOrUpdateUser.validation.ts\n\n* Merged in package/25.04-v0.9 (pull request #507)\n\nPackage/25.04 v0.9\n\n* Update login page\n\n* Update ocr in menu\n\n* Make tabs in menu links\n\n* Update menu\n\n* Remove seconds from cargo filter\n\n* Fix sorting in performance report for Nan values\n\n* Add fuel type column to consumption result\n\n* Remove empty filter options\n\n* Display only status if user can not edit readings\n\n* Add download and toggle status buttons\n\n* Add modal after statuses\n\n* Update headers and order of columns\n\n* Update and fix permissions\n\n* Update responsivness of performance report\n\n* Remove margin\n\n* Responsivess of pefromance report\n\n* Merge branch 'RASEA-901' of https://bitbucket.org/a-softy/trust-react into RASEA-901\n\n* Merge branch 'feature/RASEA-876' into package/25.04-v0.9\n\n* Merge branch 'feature/RASEA-887' into package/25.04-v0.9\n\n* Merge branch 'feature/RASEA-890' into package/25.04-v0.9\n\n* Merge branch 'feature/RASEA-893' into package/25.04-v0.9\n\n# Conflicts:\n#\tsrc/scenes/Roles/components/createOrUpdateRole.tsx\n\n* Merge branch 'f…\n* Fix styling\n\n* Update degree symbol\n\n* Update login page, for better UX\n\n* Hidden seconds from date\n\n* Fixed utc date\n\n* Added utc to string\n\n* Merge branch 'feature/RASEA-903' into package/05.05-v0.9\n\n# Conflicts:\n#\tsrc/scenes/Login/index.tsx\n\n* Merge branch 'feature/RASEA-906' into package/05.05-v0.9\n\n* Merged in package/05.05-v0.9 (pull request #508)\n\nPackage/05.05 v0.9\n\n* Merge branch 'RASEA-910-Edycja-daty-w-ekranie-cargo' into package/05.05-v0.9\n\n* Merged in package/05.05-v0.9 (pull request #509)\n\nPackage/05.05 v0.9\n\n* Revert \"Merged in package/05.05-v0.9 (pull request #509)\"\n\nThis reverts commit c988cfb92808a0e1e6161f6c724da8db20489825, reversing\nchanges made to 2bbca4cfa98bfc323a9b621826b0ac3bebd66b70.\n\n* Merge branch 'master' into release/24.04-v0.9\n", "markup": "markdown", "html": "<p>Merged in release/24.04-v0.9 (<a href=\"https://bitbucket.org/a-softy/trust-react/pull-requests/510\" rel=\"nofollow\" class=\"ap-connect-link\">pull request #510</a>)</p>\n<p>Release/24.04 v0.9</p>\n<ul>\n<li>\n<p>Merge branch 'feature/RASEA-886' into package/07.04-v0.9</p>\n</li>\n<li>\n<p>Merged in package/07.04-v0.9 (<a href=\"https://bitbucket.org/a-softy/trust-react/pull-requests/506\" rel=\"nofollow\" class=\"ap-connect-link\">pull request #506</a>)</p>\n</li>\n</ul>\n<p>Package/07.04 v0.9</p>\n<ul>\n<li>\n<p>Add validation to user form</p>\n</li>\n<li>\n<p>Add validation to role form</p>\n</li>\n<li>\n<p>Add validation to cargo form</p>\n</li>\n<li>\n<p>Fix misleading placeholder</p>\n</li>\n<li>\n<p>Add validation to meter form</p>\n</li>\n<li>\n<p>Add validation to formula form</p>\n</li>\n<li>\n<p>Add validation to fuel form</p>\n</li>\n<li>\n<p>Fix permissions for report tabs</p>\n</li>\n<li>\n<p>Add loading to role modal</p>\n</li>\n<li>\n<p>Remove ocr for menu</p>\n</li>\n<li>\n<p>Update bunkerin note page</p>\n</li>\n<li>\n<p>Merge branch 'feature/RASEA-879' into package/07.04-v0.9</p>\n</li>\n<li>\n<p>Merge branch 'feature/RASEA-873' into package/07.04-v0.9</p>\n</li>\n<li>\n<p>Merge branch 'feature/RASEA-881' into package/07.04-v0.9</p>\n</li>\n<li>\n<p>Merge branch 'feature/RASEA-886' into package/07.04-v0.9</p>\n</li>\n<li>\n<p>Fix for empty date in save storage</p>\n</li>\n<li>\n<p>Fix for formula form</p>\n</li>\n<li>\n<p>Merge branch 'feature/RASEA-879' of <a href=\"https://bitbucket.org/a-softy/trust-react\" rel=\"nofollow\" class=\"ap-connect-link\">https://bitbucket.org/a-softy/trust-react</a> into feature/RASEA-879</p>\n</li>\n<li>\n<p>Fix for user form</p>\n</li>\n<li>\n<p>Fix error message</p>\n</li>\n<li>\n<p>Merge branches 'feature/RASEA-879' and 'feature/RASEA-879' of <a href=\"https://bitbucket.org/a-softy/trust-react\" rel=\"nofollow\" class=\"ap-connect-link\">https://bitbucket.org/a-softy/trust-react</a> into feature/RASEA-879</p>\n</li>\n<li>\n<p>Fix scrubber input</p>\n</li>\n<li>\n<p>Merge branch 'feature/RASEA-879' into package/07.04-v0.9</p>\n</li>\n<li>\n<p>Merge branch 'package/07.04-v0.9' into develop</p>\n</li>\n</ul>\n<h1 id=\"markdown-header-conflicts\">Conflicts:</h1>\n<h1 id=\"markdown-header-srcscenesconsumptionformulaeindextsx\">src/scenes/ConsumptionFormulae/index.tsx</h1>\n<h1 id=\"markdown-header-srcscenesuserscomponentscreateorupdateusertsx\">src/scenes/Users/<USER>/createOrUpdateUser.tsx</h1>\n<h1 id=\"markdown-header-srcscenesuserscomponentscreateorupdateuservalidationts\">src/scenes/Users/<USER>/createOrUpdateUser.validation.ts</h1>\n<ul>\n<li>Merged in package/25.04-v0.9 (<a href=\"https://bitbucket.org/a-softy/trust-react/pull-requests/507\" rel=\"nofollow\" class=\"ap-connect-link\">pull request #507</a>)</li>\n</ul>\n<p>Package/25.04 v0.9</p>\n<ul>\n<li>\n<p>Update login page</p>\n</li>\n<li>\n<p>Update ocr in menu</p>\n</li>\n<li>\n<p>Make tabs in menu links</p>\n</li>\n<li>\n<p>Update menu</p>\n</li>\n<li>\n<p>Remove seconds from cargo filter</p>\n</li>\n<li>\n<p>Fix sorting in performance report for Nan values</p>\n</li>\n<li>\n<p>Add fuel type column to consumption result</p>\n</li>\n<li>\n<p>Remove empty filter options</p>\n</li>\n<li>\n<p>Display only status if user can not edit readings</p>\n</li>\n<li>\n<p>Add download and toggle status buttons</p>\n</li>\n<li>\n<p>Add modal after statuses</p>\n</li>\n<li>\n<p>Update headers and order of columns</p>\n</li>\n<li>\n<p>Update and fix permissions</p>\n</li>\n<li>\n<p>Update responsivness of performance report</p>\n</li>\n<li>\n<p>Remove margin</p>\n</li>\n<li>\n<p>Responsivess of pefromance report</p>\n</li>\n<li>\n<p>Merge branch 'RASEA-901' of <a href=\"https://bitbucket.org/a-softy/trust-react\" rel=\"nofollow\" class=\"ap-connect-link\">https://bitbucket.org/a-softy/trust-react</a> into RASEA-901</p>\n</li>\n<li>\n<p>Merge branch 'feature/RASEA-876' into package/25.04-v0.9</p>\n</li>\n<li>\n<p>Merge branch 'feature/RASEA-887' into package/25.04-v0.9</p>\n</li>\n<li>\n<p>Merge branch 'feature/RASEA-890' into package/25.04-v0.9</p>\n</li>\n<li>\n<p>Merge branch 'feature/RASEA-893' into package/25.04-v0.9</p>\n</li>\n</ul>\n<h1 id=\"markdown-header-conflicts_1\">Conflicts:</h1>\n<h1 id=\"markdown-header-srcscenesrolescomponentscreateorupdateroletsx\">src/scenes/Roles/components/createOrUpdateRole.tsx</h1>\n<ul>\n<li>Merge branch 'f…</li>\n<li>\n<p>Fix styling</p>\n</li>\n<li>\n<p>Update degree symbol</p>\n</li>\n<li>\n<p>Update login page, for better UX</p>\n</li>\n<li>\n<p>Hidden seconds from date</p>\n</li>\n<li>\n<p>Fixed utc date</p>\n</li>\n<li>\n<p>Added utc to string</p>\n</li>\n<li>\n<p>Merge branch 'feature/RASEA-903' into package/05.05-v0.9</p>\n</li>\n</ul>\n<h1 id=\"markdown-header-conflicts_2\">Conflicts:</h1>\n<h1 id=\"markdown-header-srcscenesloginindextsx\">src/scenes/Login/index.tsx</h1>\n<ul>\n<li>\n<p>Merge branch 'feature/RASEA-906' into package/05.05-v0.9</p>\n</li>\n<li>\n<p>Merged in package/05.05-v0.9 (<a href=\"https://bitbucket.org/a-softy/trust-react/pull-requests/508\" rel=\"nofollow\" class=\"ap-connect-link\">pull request #508</a>)</p>\n</li>\n</ul>\n<p>Package/05.05 v0.9</p>\n<ul>\n<li>\n<p>Merge branch 'RASEA-910-Edycja-daty-w-ekranie-cargo' into package/05.05-v0.9</p>\n</li>\n<li>\n<p>Merged in package/05.05-v0.9 (<a href=\"https://bitbucket.org/a-softy/trust-react/pull-requests/509\" rel=\"nofollow\" class=\"ap-connect-link\">pull request #509</a>)</p>\n</li>\n</ul>\n<p>Package/05.05 v0.9</p>\n<ul>\n<li>Revert \"Merged in package/05.05-v0.9 (<a href=\"https://bitbucket.org/a-softy/trust-react/pull-requests/509\" rel=\"nofollow\" class=\"ap-connect-link\">pull request #509</a>)\"</li>\n</ul>\n<p>This reverts commit <a href=\"https://bitbucket.org/a-softy/trust-react/commits/c988cfb92808a0e1e6161f6c724da8db20489825\" rel=\"nofollow\" class=\"ap-connect-link\">c988cfb92808a0e1e6161f6c724da8db20489825</a>, reversing<br>\nchanges made to <a href=\"https://bitbucket.org/a-softy/trust-react/commits/2bbca4cfa98bfc323a9b621826b0ac3bebd66b70\" rel=\"nofollow\" class=\"ap-connect-link\">2bbca4cfa98bfc323a9b621826b0ac3bebd66b70</a>.</p>\n<ul>\n<li>Merge branch 'master' into release/24.04-v0.9</li>\n</ul>"}}, "repository": {"type": "repository", "full_name": "a-softy/trust-react", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react"}, "avatar": {"href": "https://bytebucket.org/ravatar/%7Be9284115-4291-4cba-8bbf-6935d21923dd%7D?ts=default"}}, "name": "trust-react", "uuid": "{e9284115-4291-4cba-8bbf-6935d21923dd}"}}, {"type": "commit", "hash": "ac4d1827eca96a22fdc0ce79fc68179d6dfc09a5", "date": "2025-04-24T14:41:21+00:00", "author": {"type": "author", "raw": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "user": {"display_name": "<PERSON><PERSON><PERSON>", "links": {"self": {"href": "https://api.bitbucket.org/2.0/users/%7Bf748d839-e9f0-4bb7-a053-213de3072322%7D"}, "avatar": {"href": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/6208fd29682913007083cb49/359be245-89f5-4741-a517-b57e2aa456a8/128"}, "html": {"href": "https://bitbucket.org/%7Bf748d839-e9f0-4bb7-a053-213de3072322%7D/"}}, "type": "user", "uuid": "{f748d839-e9f0-4bb7-a053-213de3072322}", "account_id": "6208fd29682913007083cb49", "nickname": "<PERSON><PERSON><PERSON>"}}, "message": "Merge branch 'release/26.03-v0.9'\n", "summary": {"type": "rendered", "raw": "Merge branch 'release/26.03-v0.9'\n", "markup": "markdown", "html": "<p>Merge branch 'release/26.03-v0.9'</p>"}, "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/ac4d1827eca96a22fdc0ce79fc68179d6dfc09a5"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react/commits/ac4d1827eca96a22fdc0ce79fc68179d6dfc09a5"}, "diff": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/diff/ac4d1827eca96a22fdc0ce79fc68179d6dfc09a5"}, "approve": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/ac4d1827eca96a22fdc0ce79fc68179d6dfc09a5/approve"}, "comments": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/ac4d1827eca96a22fdc0ce79fc68179d6dfc09a5/comments"}, "statuses": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/ac4d1827eca96a22fdc0ce79fc68179d6dfc09a5/statuses"}}, "parents": [{"hash": "00c3b3df542fb1c155da44741ee4ca1d59940f48", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/00c3b3df542fb1c155da44741ee4ca1d59940f48"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react/commits/00c3b3df542fb1c155da44741ee4ca1d59940f48"}}, "type": "commit"}, {"hash": "9085ae287f91fc79727005c8016dce9ce9fe5ec2", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/9085ae287f91fc79727005c8016dce9ce9fe5ec2"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react/commits/9085ae287f91fc79727005c8016dce9ce9fe5ec2"}}, "type": "commit"}], "rendered": {"message": {"type": "rendered", "raw": "Merge branch 'release/26.03-v0.9'\n", "markup": "markdown", "html": "<p>Merge branch 'release/26.03-v0.9'</p>"}}, "repository": {"type": "repository", "full_name": "a-softy/trust-react", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react"}, "avatar": {"href": "https://bytebucket.org/ravatar/%7Be9284115-4291-4cba-8bbf-6935d21923dd%7D?ts=default"}}, "name": "trust-react", "uuid": "{e9284115-4291-4cba-8bbf-6935d21923dd}"}}, {"type": "commit", "hash": "9085ae287f91fc79727005c8016dce9ce9fe5ec2", "date": "2025-03-28T12:31:36+00:00", "author": {"type": "author", "raw": "<PERSON><PERSON><PERSON> <<EMAIL>>", "user": {"display_name": "<PERSON><PERSON><PERSON>", "links": {"self": {"href": "https://api.bitbucket.org/2.0/users/%7Bf748d839-e9f0-4bb7-a053-213de3072322%7D"}, "avatar": {"href": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/6208fd29682913007083cb49/359be245-89f5-4741-a517-b57e2aa456a8/128"}, "html": {"href": "https://bitbucket.org/%7Bf748d839-e9f0-4bb7-a053-213de3072322%7D/"}}, "type": "user", "uuid": "{f748d839-e9f0-4bb7-a053-213de3072322}", "account_id": "6208fd29682913007083cb49", "nickname": "<PERSON><PERSON><PERSON>"}}, "message": "cherry pick\n", "summary": {"type": "rendered", "raw": "cherry pick\n", "markup": "markdown", "html": "<p>cherry pick</p>"}, "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/9085ae287f91fc79727005c8016dce9ce9fe5ec2"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react/commits/9085ae287f91fc79727005c8016dce9ce9fe5ec2"}, "diff": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/diff/9085ae287f91fc79727005c8016dce9ce9fe5ec2"}, "approve": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/9085ae287f91fc79727005c8016dce9ce9fe5ec2/approve"}, "comments": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/9085ae287f91fc79727005c8016dce9ce9fe5ec2/comments"}, "statuses": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/9085ae287f91fc79727005c8016dce9ce9fe5ec2/statuses"}, "patch": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/patch/9085ae287f91fc79727005c8016dce9ce9fe5ec2"}}, "parents": [{"hash": "d1ce8c13ceb68687952ac2812193ceac67091f9c", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/d1ce8c13ceb68687952ac2812193ceac67091f9c"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react/commits/d1ce8c13ceb68687952ac2812193ceac67091f9c"}}, "type": "commit"}], "rendered": {"message": {"type": "rendered", "raw": "cherry pick\n", "markup": "markdown", "html": "<p>cherry pick</p>"}}, "repository": {"type": "repository", "full_name": "a-softy/trust-react", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react"}, "avatar": {"href": "https://bytebucket.org/ravatar/%7Be9284115-4291-4cba-8bbf-6935d21923dd%7D?ts=default"}}, "name": "trust-react", "uuid": "{e9284115-4291-4cba-8bbf-6935d21923dd}"}}, {"type": "commit", "hash": "00c3b3df542fb1c155da44741ee4ca1d59940f48", "date": "2025-03-28T08:23:56+00:00", "author": {"type": "author", "raw": "<PERSON><PERSON><PERSON> <<EMAIL>>", "user": {"display_name": "<PERSON><PERSON><PERSON>", "links": {"self": {"href": "https://api.bitbucket.org/2.0/users/%7Bf748d839-e9f0-4bb7-a053-213de3072322%7D"}, "avatar": {"href": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/6208fd29682913007083cb49/359be245-89f5-4741-a517-b57e2aa456a8/128"}, "html": {"href": "https://bitbucket.org/%7Bf748d839-e9f0-4bb7-a053-213de3072322%7D/"}}, "type": "user", "uuid": "{f748d839-e9f0-4bb7-a053-213de3072322}", "account_id": "6208fd29682913007083cb49", "nickname": "<PERSON><PERSON><PERSON>"}}, "message": "Merged in release/21.03-v0.9 (pull request #503)\n\nRelease/21.03 v0.9\n\n* Show ocr page\n\n(cherry picked from commit cd74aab5db344d174858e481c21f979af4e600d3)\n\n* Column fix\n\n(cherry picked from commit 83a814b7227e4f428e2960705b93e65d452709b0)\n\n* Update pipeline\n\n(cherry picked from commit d79459469a446370d28b8620e185d6598a3c49c5)\n", "summary": {"type": "rendered", "raw": "Merged in release/21.03-v0.9 (pull request #503)\n\nRelease/21.03 v0.9\n\n* Show ocr page\n\n(cherry picked from commit cd74aab5db344d174858e481c21f979af4e600d3)\n\n* Column fix\n\n(cherry picked from commit 83a814b7227e4f428e2960705b93e65d452709b0)\n\n* Update pipeline\n\n(cherry picked from commit d79459469a446370d28b8620e185d6598a3c49c5)\n", "markup": "markdown", "html": "<p>Merged in release/21.03-v0.9 (<a href=\"https://bitbucket.org/a-softy/trust-react/pull-requests/503\" rel=\"nofollow\" class=\"ap-connect-link\">pull request #503</a>)</p>\n<p>Release/21.03 v0.9</p>\n<ul>\n<li>Show ocr page</li>\n</ul>\n<p>(cherry picked from commit <a href=\"https://bitbucket.org/a-softy/trust-react/commits/cd74aab5db344d174858e481c21f979af4e600d3\" rel=\"nofollow\" class=\"ap-connect-link\">cd74aab5db344d174858e481c21f979af4e600d3</a>)</p>\n<ul>\n<li>Column fix</li>\n</ul>\n<p>(cherry picked from commit <a href=\"https://bitbucket.org/a-softy/trust-react/commits/83a814b7227e4f428e2960705b93e65d452709b0\" rel=\"nofollow\" class=\"ap-connect-link\">83a814b7227e4f428e2960705b93e65d452709b0</a>)</p>\n<ul>\n<li>Update pipeline</li>\n</ul>\n<p>(cherry picked from commit <a href=\"https://bitbucket.org/a-softy/trust-react/commits/d79459469a446370d28b8620e185d6598a3c49c5\" rel=\"nofollow\" class=\"ap-connect-link\">d79459469a446370d28b8620e185d6598a3c49c5</a>)</p>"}, "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/00c3b3df542fb1c155da44741ee4ca1d59940f48"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react/commits/00c3b3df542fb1c155da44741ee4ca1d59940f48"}, "diff": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/diff/00c3b3df542fb1c155da44741ee4ca1d59940f48"}, "approve": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/00c3b3df542fb1c155da44741ee4ca1d59940f48/approve"}, "comments": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/00c3b3df542fb1c155da44741ee4ca1d59940f48/comments"}, "statuses": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/00c3b3df542fb1c155da44741ee4ca1d59940f48/statuses"}, "patch": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/patch/00c3b3df542fb1c155da44741ee4ca1d59940f48"}}, "parents": [{"hash": "6db21d203b4319c981895c3b9caf37bb30bc29e5", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/6db21d203b4319c981895c3b9caf37bb30bc29e5"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react/commits/6db21d203b4319c981895c3b9caf37bb30bc29e5"}}, "type": "commit"}], "rendered": {"message": {"type": "rendered", "raw": "Merged in release/21.03-v0.9 (pull request #503)\n\nRelease/21.03 v0.9\n\n* Show ocr page\n\n(cherry picked from commit cd74aab5db344d174858e481c21f979af4e600d3)\n\n* Column fix\n\n(cherry picked from commit 83a814b7227e4f428e2960705b93e65d452709b0)\n\n* Update pipeline\n\n(cherry picked from commit d79459469a446370d28b8620e185d6598a3c49c5)\n", "markup": "markdown", "html": "<p>Merged in release/21.03-v0.9 (<a href=\"https://bitbucket.org/a-softy/trust-react/pull-requests/503\" rel=\"nofollow\" class=\"ap-connect-link\">pull request #503</a>)</p>\n<p>Release/21.03 v0.9</p>\n<ul>\n<li>Show ocr page</li>\n</ul>\n<p>(cherry picked from commit <a href=\"https://bitbucket.org/a-softy/trust-react/commits/cd74aab5db344d174858e481c21f979af4e600d3\" rel=\"nofollow\" class=\"ap-connect-link\">cd74aab5db344d174858e481c21f979af4e600d3</a>)</p>\n<ul>\n<li>Column fix</li>\n</ul>\n<p>(cherry picked from commit <a href=\"https://bitbucket.org/a-softy/trust-react/commits/83a814b7227e4f428e2960705b93e65d452709b0\" rel=\"nofollow\" class=\"ap-connect-link\">83a814b7227e4f428e2960705b93e65d452709b0</a>)</p>\n<ul>\n<li>Update pipeline</li>\n</ul>\n<p>(cherry picked from commit <a href=\"https://bitbucket.org/a-softy/trust-react/commits/d79459469a446370d28b8620e185d6598a3c49c5\" rel=\"nofollow\" class=\"ap-connect-link\">d79459469a446370d28b8620e185d6598a3c49c5</a>)</p>"}}, "repository": {"type": "repository", "full_name": "a-softy/trust-react", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react"}, "avatar": {"href": "https://bytebucket.org/ravatar/%7Be9284115-4291-4cba-8bbf-6935d21923dd%7D?ts=default"}}, "name": "trust-react", "uuid": "{e9284115-4291-4cba-8bbf-6935d21923dd}"}}, {"type": "commit", "hash": "d1ce8c13ceb68687952ac2812193ceac67091f9c", "date": "2025-03-26T14:49:26+00:00", "author": {"type": "author", "raw": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "user": {"display_name": "<PERSON><PERSON><PERSON>", "links": {"self": {"href": "https://api.bitbucket.org/2.0/users/%7Bf748d839-e9f0-4bb7-a053-213de3072322%7D"}, "avatar": {"href": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/6208fd29682913007083cb49/359be245-89f5-4741-a517-b57e2aa456a8/128"}, "html": {"href": "https://bitbucket.org/%7Bf748d839-e9f0-4bb7-a053-213de3072322%7D/"}}, "type": "user", "uuid": "{f748d839-e9f0-4bb7-a053-213de3072322}", "account_id": "6208fd29682913007083cb49", "nickname": "<PERSON><PERSON><PERSON>"}}, "message": "Merge branch 'feature/RASEA-865' into develop\n\n(cherry picked from commit 504ea9adfe707189de0822c46e7bb45926225941)\n", "summary": {"type": "rendered", "raw": "Merge branch 'feature/RASEA-865' into develop\n\n(cherry picked from commit 504ea9adfe707189de0822c46e7bb45926225941)\n", "markup": "markdown", "html": "<p>Merge branch 'feature/RASEA-865' into develop</p>\n<p>(cherry picked from commit <a href=\"https://bitbucket.org/a-softy/trust-react/commits/504ea9adfe707189de0822c46e7bb45926225941\" rel=\"nofollow\" class=\"ap-connect-link\">504ea9adfe707189de0822c46e7bb45926225941</a>)</p>"}, "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/d1ce8c13ceb68687952ac2812193ceac67091f9c"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react/commits/d1ce8c13ceb68687952ac2812193ceac67091f9c"}, "diff": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/diff/d1ce8c13ceb68687952ac2812193ceac67091f9c"}, "approve": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/d1ce8c13ceb68687952ac2812193ceac67091f9c/approve"}, "comments": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/d1ce8c13ceb68687952ac2812193ceac67091f9c/comments"}, "statuses": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/d1ce8c13ceb68687952ac2812193ceac67091f9c/statuses"}, "patch": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/patch/d1ce8c13ceb68687952ac2812193ceac67091f9c"}}, "parents": [{"hash": "d9c60382c69c5ac427d41035597d52e8e6205c2b", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/d9c60382c69c5ac427d41035597d52e8e6205c2b"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react/commits/d9c60382c69c5ac427d41035597d52e8e6205c2b"}}, "type": "commit"}], "rendered": {"message": {"type": "rendered", "raw": "Merge branch 'feature/RASEA-865' into develop\n\n(cherry picked from commit 504ea9adfe707189de0822c46e7bb45926225941)\n", "markup": "markdown", "html": "<p>Merge branch 'feature/RASEA-865' into develop</p>\n<p>(cherry picked from commit <a href=\"https://bitbucket.org/a-softy/trust-react/commits/504ea9adfe707189de0822c46e7bb45926225941\" rel=\"nofollow\" class=\"ap-connect-link\">504ea9adfe707189de0822c46e7bb45926225941</a>)</p>"}}, "repository": {"type": "repository", "full_name": "a-softy/trust-react", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react"}, "avatar": {"href": "https://bytebucket.org/ravatar/%7Be9284115-4291-4cba-8bbf-6935d21923dd%7D?ts=default"}}, "name": "trust-react", "uuid": "{e9284115-4291-4cba-8bbf-6935d21923dd}"}}, {"type": "commit", "hash": "d9c60382c69c5ac427d41035597d52e8e6205c2b", "date": "2025-03-26T14:46:24+00:00", "author": {"type": "author", "raw": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "user": {"display_name": "<PERSON><PERSON><PERSON>", "links": {"self": {"href": "https://api.bitbucket.org/2.0/users/%7Bf748d839-e9f0-4bb7-a053-213de3072322%7D"}, "avatar": {"href": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/6208fd29682913007083cb49/359be245-89f5-4741-a517-b57e2aa456a8/128"}, "html": {"href": "https://bitbucket.org/%7Bf748d839-e9f0-4bb7-a053-213de3072322%7D/"}}, "type": "user", "uuid": "{f748d839-e9f0-4bb7-a053-213de3072322}", "account_id": "6208fd29682913007083cb49", "nickname": "<PERSON><PERSON><PERSON>"}}, "message": "Set status to previous record\n\n(cherry picked from commit 5a2e4fd9420963e8860735b1cf4acbe02085b862)\n", "summary": {"type": "rendered", "raw": "Set status to previous record\n\n(cherry picked from commit 5a2e4fd9420963e8860735b1cf4acbe02085b862)\n", "markup": "markdown", "html": "<p>Set status to previous record</p>\n<p>(cherry picked from commit <a href=\"https://bitbucket.org/a-softy/trust-react/commits/5a2e4fd9420963e8860735b1cf4acbe02085b862\" rel=\"nofollow\" class=\"ap-connect-link\">5a2e4fd9420963e8860735b1cf4acbe02085b862</a>)</p>"}, "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/d9c60382c69c5ac427d41035597d52e8e6205c2b"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react/commits/d9c60382c69c5ac427d41035597d52e8e6205c2b"}, "diff": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/diff/d9c60382c69c5ac427d41035597d52e8e6205c2b"}, "approve": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/d9c60382c69c5ac427d41035597d52e8e6205c2b/approve"}, "comments": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/d9c60382c69c5ac427d41035597d52e8e6205c2b/comments"}, "statuses": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/d9c60382c69c5ac427d41035597d52e8e6205c2b/statuses"}, "patch": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/patch/d9c60382c69c5ac427d41035597d52e8e6205c2b"}}, "parents": [{"hash": "39185559d2a93e26c27f63952d4d0cded8ebe892", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/39185559d2a93e26c27f63952d4d0cded8ebe892"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react/commits/39185559d2a93e26c27f63952d4d0cded8ebe892"}}, "type": "commit"}], "rendered": {"message": {"type": "rendered", "raw": "Set status to previous record\n\n(cherry picked from commit 5a2e4fd9420963e8860735b1cf4acbe02085b862)\n", "markup": "markdown", "html": "<p>Set status to previous record</p>\n<p>(cherry picked from commit <a href=\"https://bitbucket.org/a-softy/trust-react/commits/5a2e4fd9420963e8860735b1cf4acbe02085b862\" rel=\"nofollow\" class=\"ap-connect-link\">5a2e4fd9420963e8860735b1cf4acbe02085b862</a>)</p>"}}, "repository": {"type": "repository", "full_name": "a-softy/trust-react", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react"}, "avatar": {"href": "https://bytebucket.org/ravatar/%7Be9284115-4291-4cba-8bbf-6935d21923dd%7D?ts=default"}}, "name": "trust-react", "uuid": "{e9284115-4291-4cba-8bbf-6935d21923dd}"}}, {"type": "commit", "hash": "39185559d2a93e26c27f63952d4d0cded8ebe892", "date": "2025-03-26T14:45:32+00:00", "author": {"type": "author", "raw": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "user": {"display_name": "<PERSON><PERSON><PERSON>", "links": {"self": {"href": "https://api.bitbucket.org/2.0/users/%7Bf748d839-e9f0-4bb7-a053-213de3072322%7D"}, "avatar": {"href": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/6208fd29682913007083cb49/359be245-89f5-4741-a517-b57e2aa456a8/128"}, "html": {"href": "https://bitbucket.org/%7Bf748d839-e9f0-4bb7-a053-213de3072322%7D/"}}, "type": "user", "uuid": "{f748d839-e9f0-4bb7-a053-213de3072322}", "account_id": "6208fd29682913007083cb49", "nickname": "<PERSON><PERSON><PERSON>"}}, "message": "Remove empty sorters\n\n(cherry picked from commit dc2585146db6a804b3c32e9ae50a9a42d6e7f99c)\n", "summary": {"type": "rendered", "raw": "Remove empty sorters\n\n(cherry picked from commit dc2585146db6a804b3c32e9ae50a9a42d6e7f99c)\n", "markup": "markdown", "html": "<p>Remove empty sorters</p>\n<p>(cherry picked from commit <a href=\"https://bitbucket.org/a-softy/trust-react/commits/dc2585146db6a804b3c32e9ae50a9a42d6e7f99c\" rel=\"nofollow\" class=\"ap-connect-link\">dc2585146db6a804b3c32e9ae50a9a42d6e7f99c</a>)</p>"}, "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/39185559d2a93e26c27f63952d4d0cded8ebe892"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react/commits/39185559d2a93e26c27f63952d4d0cded8ebe892"}, "diff": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/diff/39185559d2a93e26c27f63952d4d0cded8ebe892"}, "approve": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/39185559d2a93e26c27f63952d4d0cded8ebe892/approve"}, "comments": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/39185559d2a93e26c27f63952d4d0cded8ebe892/comments"}, "statuses": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/39185559d2a93e26c27f63952d4d0cded8ebe892/statuses"}, "patch": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/patch/39185559d2a93e26c27f63952d4d0cded8ebe892"}}, "parents": [{"hash": "b568e6b5fddc7602f266ed13e1d82e8d630c8019", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/b568e6b5fddc7602f266ed13e1d82e8d630c8019"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react/commits/b568e6b5fddc7602f266ed13e1d82e8d630c8019"}}, "type": "commit"}], "rendered": {"message": {"type": "rendered", "raw": "Remove empty sorters\n\n(cherry picked from commit dc2585146db6a804b3c32e9ae50a9a42d6e7f99c)\n", "markup": "markdown", "html": "<p>Remove empty sorters</p>\n<p>(cherry picked from commit <a href=\"https://bitbucket.org/a-softy/trust-react/commits/dc2585146db6a804b3c32e9ae50a9a42d6e7f99c\" rel=\"nofollow\" class=\"ap-connect-link\">dc2585146db6a804b3c32e9ae50a9a42d6e7f99c</a>)</p>"}}, "repository": {"type": "repository", "full_name": "a-softy/trust-react", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react"}, "avatar": {"href": "https://bytebucket.org/ravatar/%7Be9284115-4291-4cba-8bbf-6935d21923dd%7D?ts=default"}}, "name": "trust-react", "uuid": "{e9284115-4291-4cba-8bbf-6935d21923dd}"}}, {"type": "commit", "hash": "b568e6b5fddc7602f266ed13e1d82e8d630c8019", "date": "2025-03-26T14:45:14+00:00", "author": {"type": "author", "raw": "<PERSON><PERSON><PERSON> <<EMAIL>>", "user": {"display_name": "<PERSON><PERSON><PERSON>", "links": {"self": {"href": "https://api.bitbucket.org/2.0/users/%7Bf748d839-e9f0-4bb7-a053-213de3072322%7D"}, "avatar": {"href": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/6208fd29682913007083cb49/359be245-89f5-4741-a517-b57e2aa456a8/128"}, "html": {"href": "https://bitbucket.org/%7Bf748d839-e9f0-4bb7-a053-213de3072322%7D/"}}, "type": "user", "uuid": "{f748d839-e9f0-4bb7-a053-213de3072322}", "account_id": "6208fd29682913007083cb49", "nickname": "<PERSON><PERSON><PERSON>"}}, "message": "Merged in feature/RASEA-865 (pull request #499)\n\nSelect rows in between selected values\n\n* Select rows in between selected values\n\n(cherry picked from commit 6524888c7de8c80fb34b6471a0b959690a55ada6)\n", "summary": {"type": "rendered", "raw": "Merged in feature/RASEA-865 (pull request #499)\n\nSelect rows in between selected values\n\n* Select rows in between selected values\n\n(cherry picked from commit 6524888c7de8c80fb34b6471a0b959690a55ada6)\n", "markup": "markdown", "html": "<p>Merged in feature/RASEA-865 (<a href=\"https://bitbucket.org/a-softy/trust-react/pull-requests/499\" rel=\"nofollow\" class=\"ap-connect-link\">pull request #499</a>)</p>\n<p>Select rows in between selected values</p>\n<ul>\n<li>Select rows in between selected values</li>\n</ul>\n<p>(cherry picked from commit <a href=\"https://bitbucket.org/a-softy/trust-react/commits/6524888c7de8c80fb34b6471a0b959690a55ada6\" rel=\"nofollow\" class=\"ap-connect-link\">6524888c7de8c80fb34b6471a0b959690a55ada6</a>)</p>"}, "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/b568e6b5fddc7602f266ed13e1d82e8d630c8019"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react/commits/b568e6b5fddc7602f266ed13e1d82e8d630c8019"}, "diff": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/diff/b568e6b5fddc7602f266ed13e1d82e8d630c8019"}, "approve": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/b568e6b5fddc7602f266ed13e1d82e8d630c8019/approve"}, "comments": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/b568e6b5fddc7602f266ed13e1d82e8d630c8019/comments"}, "statuses": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/b568e6b5fddc7602f266ed13e1d82e8d630c8019/statuses"}, "patch": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/patch/b568e6b5fddc7602f266ed13e1d82e8d630c8019"}}, "parents": [{"hash": "d7888be575e0126ba7db39ed296255154d247220", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/d7888be575e0126ba7db39ed296255154d247220"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react/commits/d7888be575e0126ba7db39ed296255154d247220"}}, "type": "commit"}], "rendered": {"message": {"type": "rendered", "raw": "Merged in feature/RASEA-865 (pull request #499)\n\nSelect rows in between selected values\n\n* Select rows in between selected values\n\n(cherry picked from commit 6524888c7de8c80fb34b6471a0b959690a55ada6)\n", "markup": "markdown", "html": "<p>Merged in feature/RASEA-865 (<a href=\"https://bitbucket.org/a-softy/trust-react/pull-requests/499\" rel=\"nofollow\" class=\"ap-connect-link\">pull request #499</a>)</p>\n<p>Select rows in between selected values</p>\n<ul>\n<li>Select rows in between selected values</li>\n</ul>\n<p>(cherry picked from commit <a href=\"https://bitbucket.org/a-softy/trust-react/commits/6524888c7de8c80fb34b6471a0b959690a55ada6\" rel=\"nofollow\" class=\"ap-connect-link\">6524888c7de8c80fb34b6471a0b959690a55ada6</a>)</p>"}}, "repository": {"type": "repository", "full_name": "a-softy/trust-react", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react"}, "avatar": {"href": "https://bytebucket.org/ravatar/%7Be9284115-4291-4cba-8bbf-6935d21923dd%7D?ts=default"}}, "name": "trust-react", "uuid": "{e9284115-4291-4cba-8bbf-6935d21923dd}"}}, {"type": "commit", "hash": "d7888be575e0126ba7db39ed296255154d247220", "date": "2025-03-26T14:44:55+00:00", "author": {"type": "author", "raw": "<PERSON><PERSON><PERSON> <<EMAIL>>", "user": {"display_name": "<PERSON><PERSON><PERSON>", "links": {"self": {"href": "https://api.bitbucket.org/2.0/users/%7Bf748d839-e9f0-4bb7-a053-213de3072322%7D"}, "avatar": {"href": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/6208fd29682913007083cb49/359be245-89f5-4741-a517-b57e2aa456a8/128"}, "html": {"href": "https://bitbucket.org/%7Bf748d839-e9f0-4bb7-a053-213de3072322%7D/"}}, "type": "user", "uuid": "{f748d839-e9f0-4bb7-a053-213de3072322}", "account_id": "6208fd29682913007083cb49", "nickname": "<PERSON><PERSON><PERSON>"}}, "message": "Merged in feature/RASEA-862 (pull request #498)\n\nFeature/RASEA-862\n\n* Show ocr page\n\n* Column fix\n\n(cherry picked from commit 7a71e9ad9a9540f1f2cb9d1aacba40a5695e484b)\n", "summary": {"type": "rendered", "raw": "Merged in feature/RASEA-862 (pull request #498)\n\nFeature/RASEA-862\n\n* Show ocr page\n\n* Column fix\n\n(cherry picked from commit 7a71e9ad9a9540f1f2cb9d1aacba40a5695e484b)\n", "markup": "markdown", "html": "<p>Merged in feature/RASEA-862 (<a href=\"https://bitbucket.org/a-softy/trust-react/pull-requests/498\" rel=\"nofollow\" class=\"ap-connect-link\">pull request #498</a>)</p>\n<p>Feature/RASEA-862</p>\n<ul>\n<li>\n<p>Show ocr page</p>\n</li>\n<li>\n<p>Column fix</p>\n</li>\n</ul>\n<p>(cherry picked from commit <a href=\"https://bitbucket.org/a-softy/trust-react/commits/7a71e9ad9a9540f1f2cb9d1aacba40a5695e484b\" rel=\"nofollow\" class=\"ap-connect-link\">7a71e9ad9a9540f1f2cb9d1aacba40a5695e484b</a>)</p>"}, "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/d7888be575e0126ba7db39ed296255154d247220"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react/commits/d7888be575e0126ba7db39ed296255154d247220"}, "diff": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/diff/d7888be575e0126ba7db39ed296255154d247220"}, "approve": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/d7888be575e0126ba7db39ed296255154d247220/approve"}, "comments": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/d7888be575e0126ba7db39ed296255154d247220/comments"}, "statuses": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/d7888be575e0126ba7db39ed296255154d247220/statuses"}, "patch": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/patch/d7888be575e0126ba7db39ed296255154d247220"}}, "parents": [{"hash": "cc1cc36d5b2d90cbac8f65c68be19206f225d30b", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/cc1cc36d5b2d90cbac8f65c68be19206f225d30b"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react/commits/cc1cc36d5b2d90cbac8f65c68be19206f225d30b"}}, "type": "commit"}], "rendered": {"message": {"type": "rendered", "raw": "Merged in feature/RASEA-862 (pull request #498)\n\nFeature/RASEA-862\n\n* Show ocr page\n\n* Column fix\n\n(cherry picked from commit 7a71e9ad9a9540f1f2cb9d1aacba40a5695e484b)\n", "markup": "markdown", "html": "<p>Merged in feature/RASEA-862 (<a href=\"https://bitbucket.org/a-softy/trust-react/pull-requests/498\" rel=\"nofollow\" class=\"ap-connect-link\">pull request #498</a>)</p>\n<p>Feature/RASEA-862</p>\n<ul>\n<li>\n<p>Show ocr page</p>\n</li>\n<li>\n<p>Column fix</p>\n</li>\n</ul>\n<p>(cherry picked from commit <a href=\"https://bitbucket.org/a-softy/trust-react/commits/7a71e9ad9a9540f1f2cb9d1aacba40a5695e484b\" rel=\"nofollow\" class=\"ap-connect-link\">7a71e9ad9a9540f1f2cb9d1aacba40a5695e484b</a>)</p>"}}, "repository": {"type": "repository", "full_name": "a-softy/trust-react", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react"}, "avatar": {"href": "https://bytebucket.org/ravatar/%7Be9284115-4291-4cba-8bbf-6935d21923dd%7D?ts=default"}}, "name": "trust-react", "uuid": "{e9284115-4291-4cba-8bbf-6935d21923dd}"}}, {"type": "commit", "hash": "cc1cc36d5b2d90cbac8f65c68be19206f225d30b", "date": "2025-03-26T14:44:43+00:00", "author": {"type": "author", "raw": "<PERSON><PERSON><PERSON> <<EMAIL>>", "user": {"display_name": "<PERSON><PERSON><PERSON>", "links": {"self": {"href": "https://api.bitbucket.org/2.0/users/%7Bf748d839-e9f0-4bb7-a053-213de3072322%7D"}, "avatar": {"href": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/6208fd29682913007083cb49/359be245-89f5-4741-a517-b57e2aa456a8/128"}, "html": {"href": "https://bitbucket.org/%7Bf748d839-e9f0-4bb7-a053-213de3072322%7D/"}}, "type": "user", "uuid": "{f748d839-e9f0-4bb7-a053-213de3072322}", "account_id": "6208fd29682913007083cb49", "nickname": "<PERSON><PERSON><PERSON>"}}, "message": "Merge RASEA-850\n", "summary": {"type": "rendered", "raw": "Merge RASEA-850\n", "markup": "markdown", "html": "<p>Merge RASEA-850</p>"}, "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/cc1cc36d5b2d90cbac8f65c68be19206f225d30b"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react/commits/cc1cc36d5b2d90cbac8f65c68be19206f225d30b"}, "diff": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/diff/cc1cc36d5b2d90cbac8f65c68be19206f225d30b"}, "approve": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/cc1cc36d5b2d90cbac8f65c68be19206f225d30b/approve"}, "comments": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/cc1cc36d5b2d90cbac8f65c68be19206f225d30b/comments"}, "statuses": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/cc1cc36d5b2d90cbac8f65c68be19206f225d30b/statuses"}, "patch": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/patch/cc1cc36d5b2d90cbac8f65c68be19206f225d30b"}}, "parents": [{"hash": "a0a69065244b14667476231d989ea3f6155c68b8", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commit/a0a69065244b14667476231d989ea3f6155c68b8"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react/commits/a0a69065244b14667476231d989ea3f6155c68b8"}}, "type": "commit"}], "rendered": {"message": {"type": "rendered", "raw": "Merge RASEA-850\n", "markup": "markdown", "html": "<p>Merge RASEA-850</p>"}}, "repository": {"type": "repository", "full_name": "a-softy/trust-react", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react"}, "html": {"href": "https://bitbucket.org/a-softy/trust-react"}, "avatar": {"href": "https://bytebucket.org/ravatar/%7Be9284115-4291-4cba-8bbf-6935d21923dd%7D?ts=default"}}, "name": "trust-react", "uuid": "{e9284115-4291-4cba-8bbf-6935d21923dd}"}}], "pagelen": 10, "next": "https://api.bitbucket.org/2.0/repositories/a-softy/trust-react/commits/master?pagelen=10&ctx=437f372bb6bdd4ee7dfbb41d752a01dd&page=2"}