#!/usr/bin/env py
# -*- coding: utf-8 -*-

import pandas as pd
import requests
import json
import base64
import re
from datetime import datetime

# Konfiguracja JIRA
JIRA_URL = "https://read-at-sea.atlassian.net"
EMAIL = "j.wik<PERSON><PERSON>@a-soft.pl"
API_TOKEN = "ATATT3xFfGF0o1tVy4he6mvThd2U0mZU2NCqHbZoCB216R6aPWZZUnKecNaRaTIFIBkmb3sTHDvEPtjnJ__aW1EGolFwrIFPuo5qY9Z4SEW2TlMMt8d8v0sLWKkGGMkjLSDQQyP2wT4BImUn_XaNGozE1-KzMj1GFxhAtdkdmwtMErw0hkwPo4Q=D1D6CC05"

def extract_text_from_jira_content(content):
    """Wyciąga tekst z struktury JIRA content (JSON)"""
    if not content:
        return ""

    if isinstance(content, str):
        return content

    if isinstance(content, dict):
        text_parts = []

        # Sprawdź czy to struktura Atlassian Document Format
        if content.get('type') == 'doc' and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))

        # Sprawdź inne typy elementów
        elif content.get('type') == 'paragraph' and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))

        elif content.get('type') == 'text' and 'text' in content:
            text_parts.append(content['text'])

        elif content.get('type') in ['heading', 'codeBlock', 'blockquote'] and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))

        # Jeśli to lista
        elif content.get('type') in ['bulletList', 'orderedList'] and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))

        elif content.get('type') == 'listItem' and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))

        return ' '.join(text_parts)

    elif isinstance(content, list):
        text_parts = []
        for item in content:
            text_parts.append(extract_text_from_jira_content(item))
        return ' '.join(text_parts)

    return str(content)

def get_jira_issue_with_comments(issue_key):
    """Pobiera szczegóły zadania z JIRA wraz z komentarzami"""

    # Przygotowanie autoryzacji
    auth_string = f"{EMAIL}:{API_TOKEN}"
    auth_bytes = auth_string.encode('ascii')
    auth_b64 = base64.b64encode(auth_bytes).decode('ascii')

    headers = {
        'Authorization': f'Basic {auth_b64}',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }

    # Pobierz podstawowe informacje o zadaniu
    url = f"{JIRA_URL}/rest/api/3/issue/{issue_key}"
    params = {
        'fields': 'key,summary,status,assignee,reporter,created,updated,priority,issuetype,description,components,labels,comment'
    }

    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        data = response.json()

        # Przetwórz opis z formatu JIRA na tekst
        if data.get('fields', {}).get('description'):
            description_content = data['fields']['description']
            data['fields']['description_text'] = extract_text_from_jira_content(description_content)
            data['fields']['description_raw'] = description_content

        # Przetwórz komentarze
        comments_text = []
        if data.get('fields', {}).get('comment', {}).get('comments'):
            for comment in data['fields']['comment']['comments']:
                if comment.get('body'):
                    comment_text = extract_text_from_jira_content(comment['body'])
                    author = comment.get('author', {}).get('displayName', 'Nieznany')
                    created = comment.get('created', '')
                    comments_text.append(f"[{author}, {created[:10]}]: {comment_text}")

        data['fields']['comments_text'] = comments_text
        data['fields']['all_comments_combined'] = ' | '.join(comments_text)

        return data
    except requests.exceptions.RequestException as e:
        print(f"Błąd podczas pobierania zadania {issue_key}: {e}")
        return None

def get_jira_issue(issue_key):
    """Wrapper dla kompatybilności wstecznej"""
    return get_jira_issue_with_comments(issue_key)

def extract_jira_links_from_text(text):
    """Wyciąga linki do JIRA z tekstu"""
    if not text or not isinstance(text, str):
        return []

    # Wzorce dla linków JIRA
    patterns = [
        r'https://read-at-sea\.atlassian\.net/[^\s]+',
        r'https://[^/]+\.atlassian\.net/[^\s]+',
    ]

    links = []
    for pattern in patterns:
        matches = re.findall(pattern, text)
        links.extend(matches)

    return list(set(links))

def extract_jira_keys_from_links(links):
    """Wyciąga numery JIRA z linków"""
    jira_keys = []

    for link in links:
        # Sprawdź czy link zawiera projekt RS
        if 'project%20%3D%20%22RS%22' in link or 'project=RS' in link:
            # To jest link do projektu RS - pobierz zadania z tego projektu
            try:
                # Pobierz zadania z projektu RS
                auth_string = f"{EMAIL}:{API_TOKEN}"
                auth_bytes = auth_string.encode('ascii')
                auth_b64 = base64.b64encode(auth_bytes).decode('ascii')

                headers = {
                    'Authorization': f'Basic {auth_b64}',
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }

                search_url = f"{JIRA_URL}/rest/api/3/search"
                params = {
                    'jql': 'project = RS ORDER BY created DESC',
                    'maxResults': 10,  # Ograniczenie do 10 najnowszych
                    'fields': 'key'
                }

                response = requests.get(search_url, headers=headers, params=params)
                if response.status_code == 200:
                    search_data = response.json()
                    for issue in search_data.get('issues', []):
                        jira_keys.append(issue['key'])
                    print(f"Znaleziono {len(jira_keys)} zadań z projektu RS przez link")
            except Exception as e:
                print(f"Błąd podczas pobierania zadań z linku {link}: {e}")

        # Sprawdź czy link zawiera konkretny numer zadania
        jira_key_matches = re.findall(r'([A-Z]+-\d+)', link)
        jira_keys.extend(jira_key_matches)

    return list(set(jira_keys))

def extract_jira_keys(text):
    """Wyciąga numery JIRA z tekstu, w tym z linków"""
    if pd.isna(text) or not isinstance(text, str):
        return []

    jira_keys = []

    # 1. Znajdź bezpośrednie numery JIRA
    patterns = [
        r'\b(RS-\d+)\b',
        r'\b(RASEA-\d+)\b',
        r'\b([A-Z]+-\d+)\b'
    ]

    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        jira_keys.extend(matches)

    # 2. Znajdź linki JIRA i wyciągnij z nich numery
    jira_links = extract_jira_links_from_text(text)
    if jira_links:
        print(f"Znaleziono linki JIRA: {jira_links}")
        linked_keys = extract_jira_keys_from_links(jira_links)
        jira_keys.extend(linked_keys)

    return list(set(jira_keys))  # Usuń duplikaty

def analyze_row_with_jira(simon_comment, description_text="", task_text="", row_data=None, jira_keys_in_row=None, all_jira_keys_in_file=None, all_jira_details=None, current_row_number=None):
    """Analizuje wiersz z numerami JIRA i generuje 3 kolumny danych"""

    if jira_keys_in_row is None:
        jira_keys_in_row = []
    if all_jira_keys_in_file is None:
        all_jira_keys_in_file = {}
    if all_jira_details is None:
        all_jira_details = {}

    # Jeśli brak numerów JIRA i komentarza Simona, zwróć puste odpowiedzi
    if not jira_keys_in_row and (pd.isna(simon_comment) or not isinstance(simon_comment, str) or not simon_comment.strip()):
        return {"dane_zrodlowe": "", "jira_ticket": "", "analiza_jarka": ""}

    # KOLUMNA 1: DANE ŹRÓDŁOWE
    dane_zrodlowe = "**DANE ŹRÓDŁOWE UŻYTE DO ANALIZY:**\n\n"

    if simon_comment and simon_comment.strip():
        dane_zrodlowe += f"• **Komentarz Simona:** {simon_comment}\n"

    if description_text.strip():
        dane_zrodlowe += f"• **Opis zadania:** {description_text.strip()}\n"
    if task_text.strip():
        dane_zrodlowe += f"• **Nazwa zadania:** {task_text.strip()}\n"

    # Dodaj inne istotne dane z wiersza
    if row_data:
        for col, value in row_data.items():
            if pd.notna(value) and isinstance(value, str) and value.strip():
                col_lower = str(col).lower()
                if any(word in col_lower for word in ['project', 'client', 'category', 'tags']):
                    dane_zrodlowe += f"• **{col}:** {value}\n"

    if jira_keys_in_row:
        dane_zrodlowe += f"• **Znalezione numery JIRA:** {', '.join(jira_keys_in_row)}\n"

    # KOLUMNA 2: JIRA TICKET
    jira_ticket = ""

    if jira_keys_in_row:
        jira_ticket = "**SZCZEGÓŁY ZADAŃ Z JIRA:**\n\n"

        for jira_key in jira_keys_in_row:
            # Sprawdź czy to pierwsze wystąpienie tego klucza w pliku
            first_occurrence_rows = all_jira_keys_in_file.get(jira_key, [])
            first_row = min(first_occurrence_rows) if first_occurrence_rows else current_row_number

            if first_row < current_row_number:
                # To jest duplikat
                jira_ticket += f"**{jira_key}** - PATRZ SZCZEGÓŁY W WIERSZU {first_row}\n\n"
            else:
                # To jest pierwsze wystąpienie - pokaż pełne szczegóły
                if jira_key in all_jira_details:
                    fields = all_jira_details[jira_key]

                    jira_ticket += f"**{jira_key}**\n"
                    jira_ticket += f"• **Tytuł:** {fields.get('summary', 'Brak tytułu')}\n"
                    jira_ticket += f"• **Status:** {fields.get('status', {}).get('name', 'Nieznany')}\n"
                    jira_ticket += f"• **Typ:** {fields.get('issuetype', {}).get('name', 'Nieznany')}\n"
                    jira_ticket += f"• **Priorytet:** {fields.get('priority', {}).get('name', 'Nieznany')}\n"

                    if fields.get('assignee'):
                        jira_ticket += f"• **Przypisany do:** {fields['assignee'].get('displayName', 'Nieznany')}\n"

                    if fields.get('description_text'):
                        jira_ticket += f"• **Opis:** {fields['description_text']}\n"

                    if fields.get('comments_text') and len(fields['comments_text']) > 0:
                        jira_ticket += f"• **Komentarze ({len(fields['comments_text'])}):**\n"
                        for i, comment in enumerate(fields['comments_text'][:3]):  # Pokaż max 3 komentarze
                            jira_ticket += f"  {i+1}. {comment}\n"
                        if len(fields['comments_text']) > 3:
                            jira_ticket += f"  ... i {len(fields['comments_text'])-3} więcej komentarzy\n"

                    if fields.get('labels'):
                        jira_ticket += f"• **Etykiety:** {', '.join(fields['labels'])}\n"

                    if fields.get('components'):
                        components = [comp.get('name', '') for comp in fields['components']]
                        jira_ticket += f"• **Komponenty:** {', '.join(components)}\n"

                    jira_ticket += "\n"
                else:
                    jira_ticket += f"**{jira_key}** - Nie udało się pobrać szczegółów\n\n"

    # KOLUMNA 3: ANALIZA JARKA
    analiza_jarka = ""

    if jira_keys_in_row:
        # Sprawdź czy wszystkie klucze to duplikaty
        all_duplicates = True
        first_occurrences = []

        for jira_key in jira_keys_in_row:
            first_occurrence_rows = all_jira_keys_in_file.get(jira_key, [])
            first_row = min(first_occurrence_rows) if first_occurrence_rows else current_row_number

            if first_row >= current_row_number:
                all_duplicates = False
            else:
                first_occurrences.append(f"{jira_key} (wiersz {first_row})")

        if all_duplicates and first_occurrences:
            analiza_jarka = f"**PATRZ ANALIZĘ W WIERSZACH:** {', '.join(first_occurrences)}"
        else:
            # Wykonaj pełną analizę
            jira_details_for_analysis = {}
            for jira_key in jira_keys_in_row:
                if jira_key in all_jira_details:
                    jira_details_for_analysis[jira_key] = all_jira_details[jira_key]

            classification = classify_issue_type(simon_comment, description_text, task_text, row_data, jira_details_for_analysis)

            analiza_jarka = f"{classification['header']}\n\n"
            analiza_jarka += f"**UZASADNIENIE KLASYFIKACJI:**\n{classification['explanation']}\n\n"
            analiza_jarka += f"**REKOMENDOWANE DZIAŁANIA:**\n{classification['recommendations']}"

            # Dodaj odnośniki do duplikatów jeśli są
            if first_occurrences:
                analiza_jarka += f"\n\n**INNE WYSTĄPIENIA:** {', '.join(first_occurrences)}"

    elif simon_comment and simon_comment.strip():
        # Komentarz Simona bez numerów JIRA
        classification = classify_issue_type(simon_comment, description_text, task_text, row_data, {})

        analiza_jarka = f"{classification['header']}\n\n"
        analiza_jarka += f"**UZASADNIENIE KLASYFIKACJI:**\n{classification['explanation']}\n\n"
        analiza_jarka += f"**REKOMENDOWANE DZIAŁANIA:**\n{classification['recommendations']}"

    return {
        "dane_zrodlowe": dane_zrodlowe,
        "jira_ticket": jira_ticket,
        "analiza_jarka": analiza_jarka
    }

def analyze_simon_comment(simon_comment, description_text="", task_text="", row_data=None, processed_jira_keys=None, all_jira_keys_in_file=None, current_row_number=None, df_for_reference=None):
    """Analizuje komentarz Simona i generuje 3 kolumny danych"""

    if pd.isna(simon_comment) or not isinstance(simon_comment, str):
        return {"dane_zrodlowe": "", "jira_ticket": "", "analiza_jarka": ""}

    if processed_jira_keys is None:
        processed_jira_keys = set()
    if all_jira_keys_in_file is None:
        all_jira_keys_in_file = {}

    # Znajdź numery JIRA w komentarzu, opisie lub nazwie zadania
    all_text = f"{simon_comment} {description_text} {task_text}"
    jira_keys = extract_jira_keys(all_text)

    # Sprawdź czy to są już przetworzone numery JIRA
    duplicate_jira_keys = []
    new_jira_keys = []

    for jira_key in jira_keys:
        # Sprawdź czy ten klucz już wystąpił wcześniej w pliku
        first_occurrence_rows = all_jira_keys_in_file.get(jira_key, [])
        earlier_rows = [r for r in first_occurrence_rows if r < current_row_number]

        if earlier_rows:
            # To jest duplikat - znajdź pierwszy wiersz gdzie wystąpił
            first_row = min(earlier_rows)

            # Sprawdź czy pierwszy wiersz ma komentarz Simona (będzie miał analizę)
            has_simon_comment = False
            if df_for_reference is not None:
                try:
                    first_row_data = df_for_reference.iloc[first_row - 1]  # -1 bo DataFrame jest 0-indexed
                    simon_col_name = None
                    for col in df_for_reference.columns:
                        if 'simon' in str(col).lower():
                            simon_col_name = col
                            break

                    if simon_col_name and pd.notna(first_row_data.get(simon_col_name)) and str(first_row_data.get(simon_col_name)).strip():
                        has_simon_comment = True
                except:
                    pass

            duplicate_jira_keys.append((jira_key, first_row, has_simon_comment))
        else:
            # To jest pierwsze wystąpienie tego klucza
            new_jira_keys.append(jira_key)

    # Dodaj nowe klucze do zbioru przetworzonych (tylko jeśli to rzeczywiście pierwsze wystąpienie)
    processed_jira_keys.update(new_jira_keys)

    # KOLUMNA 1: DANE ŹRÓDŁOWE
    dane_zrodlowe = "**DANE ŹRÓDŁOWE UŻYTE DO ANALIZY:**\n\n"
    dane_zrodlowe += f"• **Komentarz Simona:** {simon_comment}\n"
    if description_text.strip():
        dane_zrodlowe += f"• **Opis zadania:** {description_text.strip()}\n"
    if task_text.strip():
        dane_zrodlowe += f"• **Nazwa zadania:** {task_text.strip()}\n"

    # Dodaj inne istotne dane z wiersza
    if row_data:
        for col, value in row_data.items():
            if pd.notna(value) and isinstance(value, str) and value.strip():
                col_lower = str(col).lower()
                if any(word in col_lower for word in ['project', 'client', 'category', 'tags']):
                    dane_zrodlowe += f"• **{col}:** {value}\n"

    if jira_keys:
        dane_zrodlowe += f"• **Znalezione numery JIRA:** {', '.join(jira_keys)}\n"

    # KOLUMNA 2: JIRA TICKET
    jira_ticket = ""
    jira_details = {}

    if duplicate_jira_keys and not new_jira_keys:
        references = []
        for jira_key, first_row, has_simon_comment in duplicate_jira_keys:
            if has_simon_comment and first_row:
                references.append(f"{jira_key} (patrz wiersz {first_row})")
            else:
                # Jeśli pierwszy wiersz nie ma komentarza Simona, pobierz szczegóły z JIRA
                jira_issue = get_jira_issue(jira_key)
                if jira_issue:
                    fields = jira_issue.get('fields', {})
                    jira_ticket += f"\n**{jira_key}** (brak wcześniejszej analizy)\n"
                    jira_ticket += f"• **Tytuł:** {fields.get('summary', 'Brak tytułu')}\n"
                    jira_ticket += f"• **Status:** {fields.get('status', {}).get('name', 'Nieznany')}\n"
                    jira_ticket += f"• **Typ:** {fields.get('issuetype', {}).get('name', 'Nieznany')}\n"
                    if fields.get('description_text'):
                        jira_ticket += f"• **Opis:** {fields['description_text']}\n"
                    jira_ticket += "\n"
                    continue

        if references:
            jira_ticket = f"**PATRZ WYŻEJ SZCZEGÓŁY** dla {', '.join(references)}"
    elif new_jira_keys:
        jira_ticket = "**SZCZEGÓŁY ZADAŃ Z JIRA:**\n\n"
        for jira_key in new_jira_keys:
            jira_issue = get_jira_issue(jira_key)
            if jira_issue:
                fields = jira_issue.get('fields', {})
                jira_details[jira_key] = fields

                jira_ticket += f"**{jira_key}**\n"
                jira_ticket += f"• **Tytuł:** {fields.get('summary', 'Brak tytułu')}\n"
                jira_ticket += f"• **Status:** {fields.get('status', {}).get('name', 'Nieznany')}\n"
                jira_ticket += f"• **Typ:** {fields.get('issuetype', {}).get('name', 'Nieznany')}\n"
                jira_ticket += f"• **Priorytet:** {fields.get('priority', {}).get('name', 'Nieznany')}\n"

                if fields.get('assignee'):
                    jira_ticket += f"• **Przypisany do:** {fields['assignee'].get('displayName', 'Nieznany')}\n"

                if fields.get('description_text'):
                    jira_ticket += f"• **Opis:** {fields['description_text']}\n"

                if fields.get('labels'):
                    jira_ticket += f"• **Etykiety:** {', '.join(fields['labels'])}\n"

                if fields.get('components'):
                    components = [comp.get('name', '') for comp in fields['components']]
                    jira_ticket += f"• **Komponenty:** {', '.join(components)}\n"

                jira_ticket += "\n"
            else:
                jira_ticket += f"**{jira_key}** - Nie udało się pobrać szczegółów\n\n"

    # KOLUMNA 3: ANALIZA JARKA
    if duplicate_jira_keys and not new_jira_keys:
        references = []
        need_new_analysis = []

        for jira_key, first_row, has_simon_comment in duplicate_jira_keys:
            if has_simon_comment and first_row:
                references.append(f"{jira_key} (patrz wiersz {first_row})")
            else:
                need_new_analysis.append(jira_key)

        if references and not need_new_analysis:
            analiza_jarka = f"**PATRZ WYŻEJ ANALIZĘ** dla {', '.join(references)}"
        elif need_new_analysis:
            # Pobierz szczegóły z JIRA i wykonaj analizę
            jira_details = {}
            for jira_key in need_new_analysis:
                jira_issue = get_jira_issue(jira_key)
                if jira_issue:
                    jira_details[jira_key] = jira_issue.get('fields', {})

            # Wykonaj klasyfikację
            classification = classify_issue_type(simon_comment, description_text, task_text, row_data, jira_details)
            analiza_jarka = f"{classification['header']}\n\n"
            analiza_jarka += f"**UZASADNIENIE KLASYFIKACJI:**\n{classification['explanation']}\n\n"
            analiza_jarka += f"**REKOMENDOWANE DZIAŁANIA:**\n{classification['recommendations']}"

            if references:
                analiza_jarka += f"\n\n**DODATKOWO PATRZ:** {', '.join(references)}"
        else:
            analiza_jarka = f"**PATRZ WYŻEJ ANALIZĘ** dla {', '.join(references)}"
    else:
        # Zaawansowana analiza typu na podstawie wszystkich dostępnych danych
        classification = classify_issue_type(simon_comment, description_text, task_text, row_data, jira_details)

        analiza_jarka = f"{classification['header']}\n\n"
        analiza_jarka += f"**UZASADNIENIE KLASYFIKACJI:**\n{classification['explanation']}\n\n"
        analiza_jarka += f"**REKOMENDOWANE DZIAŁANIA:**\n{classification['recommendations']}"

    return {
        "dane_zrodlowe": dane_zrodlowe,
        "jira_ticket": jira_ticket,
        "analiza_jarka": analiza_jarka
    }

def classify_issue_type(simon_comment, description_text, task_text, row_data, jira_details):
    """Klasyfikuje typ zagadnienia na podstawie wszystkich dostępnych danych"""

    # Połącz wszystkie teksty do analizy
    all_text = f"{simon_comment} {description_text} {task_text}".lower()

    # Dodaj dane z wiersza jeśli dostępne
    if row_data is not None:
        for col, value in row_data.items():
            if pd.notna(value) and isinstance(value, str):
                all_text += f" {value}".lower()

    # Dodaj dane z JIRA
    jira_text = ""
    jira_types = []
    jira_descriptions = []
    jira_comments = []
    jira_sources = []

    for jira_key, fields in jira_details.items():
        jira_sources.append(jira_key)

        if fields.get('summary'):
            jira_text += f" {fields['summary']}".lower()
        if fields.get('description_text'):
            jira_text += f" {fields['description_text']}".lower()
            jira_descriptions.append(f"[{jira_key}] {fields['description_text']}")
        if fields.get('all_comments_combined'):
            jira_text += f" {fields['all_comments_combined']}".lower()
            jira_comments.append(f"[{jira_key}] {fields['all_comments_combined']}")
        if fields.get('issuetype', {}).get('name'):
            jira_types.append(fields['issuetype']['name'].lower())
        if fields.get('labels'):
            for label in fields['labels']:
                jira_text += f" {label}".lower()

    all_text += jira_text

    # Słowa kluczowe dla różnych kategorii
    bug_keywords = [
        'bug', 'błąd', 'error', 'problem', 'issue', 'defect', 'crash', 'fail', 'broken',
        'not working', 'nie działa', 'exception', 'null', 'undefined', 'incorrect',
        'wrong', 'fix', 'repair', 'solve', 'resolve', 'debug', 'malfunction', 'fault'
    ]

    improvement_keywords = [
        'improvement', 'enhance', 'feature', 'add', 'improve', 'usprawnienie', 'funkcja',
        'new', 'better', 'optimize', 'performance', 'upgrade', 'extend', 'modify',
        'change', 'update', 'refactor', 'redesign', 'implement', 'dodawanie', 'dodanie',
        'tworzenie', 'utworzenie', 'rozszerzenie', 'parametrów', 'parametry', 'funkcjonalność'
    ]

    question_keywords = [
        'question', 'pytanie', 'how', 'why', 'what', 'jak', 'dlaczego', 'co',
        'explain', 'clarify', 'understand', 'meaning', 'purpose'
    ]

    test_keywords = [
        'test', 'testing', 'verify', 'check', 'sprawdź', 'validate', 'confirm',
        'review', 'qa', 'quality', 'acceptance'
    ]

    # Liczenie wystąpień słów kluczowych
    bug_score = sum(1 for word in bug_keywords if word in all_text)
    improvement_score = sum(1 for word in improvement_keywords if word in all_text)
    question_score = sum(1 for word in question_keywords if word in all_text)
    test_score = sum(1 for word in test_keywords if word in all_text)

    # Dodatkowe punkty na podstawie typu w JIRA
    if any('bug' in jtype for jtype in jira_types):
        bug_score += 3
    if any('story' in jtype or 'feature' in jtype or 'improvement' in jtype for jtype in jira_types):
        improvement_score += 3
    if any('task' in jtype for jtype in jira_types):
        improvement_score += 1

    # Określ typ na podstawie najwyższego wyniku
    scores = {
        'bug': bug_score,
        'improvement': improvement_score,
        'question': question_score,
        'test': test_score
    }

    max_score = max(scores.values())
    if max_score == 0:
        issue_type = 'general'
    else:
        issue_type = max(scores, key=scores.get)

    # Przygotuj odpowiedź
    if issue_type == 'bug':
        header = "🐛 **BUG** - "
        explanation = f"Sklasyfikowane jako BUG na podstawie:\n"
        explanation += f"- Słowa kluczowe wskazujące na błąd (wynik: {bug_score})\n"
        if jira_sources:
            explanation += f"- Źródła danych: {', '.join(jira_sources)}\n"
        if any('bug' in jtype for jtype in jira_types):
            explanation += f"- Typ zadania w JIRA: {', '.join(jira_types)}\n"
        if jira_descriptions:
            explanation += f"- Opisy z JIRA: {' | '.join(jira_descriptions[:2])}\n"
        if jira_comments:
            explanation += f"- Komentarze z JIRA: {' | '.join(jira_comments[:2])}\n"
        explanation += f"- Kontekst wskazuje na nieprawidłowe działanie systemu\n"
        explanation += f"- Wymaga naprawy istniejącej funkcjonalności"

        recommendations = """1. Zreprodukować błąd w środowisku testowym
2. Zidentyfikować przyczynę źródłową (root cause analysis)
3. Opracować i przetestować poprawkę
4. Przeprowadzić testy regresyjne
5. Wdrożyć na produkcję z odpowiednimi testami"""

    elif issue_type == 'improvement':
        header = "✨ **IMPROVEMENT** - "
        explanation = f"Sklasyfikowane jako IMPROVEMENT na podstawie:\n"
        explanation += f"- Słowa kluczowe wskazujące na usprawnienie (wynik: {improvement_score})\n"
        if jira_sources:
            explanation += f"- Źródła danych: {', '.join(jira_sources)}\n"
        if any('story' in jtype or 'feature' in jtype or 'improvement' in jtype for jtype in jira_types):
            explanation += f"- Typ zadania w JIRA: {', '.join(jira_types)}\n"
        if jira_descriptions:
            explanation += f"- Opisy z JIRA: {' | '.join(jira_descriptions[:2])}\n"
        if jira_comments:
            explanation += f"- Komentarze z JIRA: {' | '.join(jira_comments[:2])}\n"
        explanation += f"- Kontekst wskazuje na dodanie nowej funkcjonalności\n"
        explanation += f"- Dotyczy rozszerzenia lub poprawy istniejących możliwości"

        recommendations = """1. Przeanalizować wpływ na istniejącą funkcjonalność
2. Oszacować nakład pracy i zasoby
3. Zaplanować implementację w odpowiednim sprincie
4. Przygotować testy akceptacyjne i dokumentację
5. Skonsultować z zespołem UX/UI jeśli potrzeba"""

    elif issue_type == 'question':
        header = "❓ **PYTANIE** - "
        explanation = f"Sklasyfikowane jako PYTANIE na podstawie:\n"
        explanation += f"- Słowa kluczowe wskazujące na pytanie (wynik: {question_score})\n"
        explanation += f"- Kontekst wymaga wyjaśnienia lub dodatkowych informacji\n"
        explanation += f"- Potrzebne jest zrozumienie wymagań lub funkcjonalności"

        recommendations = """1. Przeanalizować szczegółowo pytanie
2. Zebrać dodatkowe informacje jeśli potrzeba
3. Skonsultować z zespołem lub ekspertami
4. Przygotować szczegółową odpowiedź
5. Udokumentować rozwiązanie dla przyszłości"""

    elif issue_type == 'test':
        header = "🧪 **TESTOWANIE** - "
        explanation = f"Sklasyfikowane jako TESTOWANIE na podstawie:\n"
        explanation += f"- Słowa kluczowe wskazujące na testy (wynik: {test_score})\n"
        explanation += f"- Kontekst wymaga weryfikacji lub sprawdzenia\n"
        explanation += f"- Dotyczy kontroli jakości lub akceptacji"

        recommendations = """1. Przygotować plan testów
2. Wykonać testy funkcjonalne i niefunkcjonalne
3. Udokumentować wyniki testów
4. Zgłosić ewentualne błędy lub problemy
5. Potwierdzić akceptację funkcjonalności"""

    else:
        header = "📝 **UWAGA** - "
        explanation = f"Wymaga dodatkowej analizy:\n"
        explanation += f"- Brak jednoznacznych wskaźników typu (bug: {bug_score}, improvement: {improvement_score})\n"
        explanation += f"- Kontekst wymaga głębszej analizy\n"
        explanation += f"- Może wymagać konsultacji z zespołem"

        recommendations = """1. Przeanalizować szczegółowo kontekst
2. Skonsultować z zespołem lub klientem
3. Określić priorytet i typ działania
4. Zaplanować odpowiednie kroki
5. Monitorować postęp i wyniki"""

    return {
        'header': header,
        'explanation': explanation,
        'recommendations': recommendations,
        'type': issue_type,
        'scores': scores
    }

def process_clockify_file():
    """Przetwarza plik Clockify i dodaje kolumnę z odpowiedziami Jarka"""

    filename = "clockify_report.xlsx"

    try:
        # Wczytaj plik Excel
        print(f"Wczytywanie pliku: {filename}")

        # Sprawdź dostępne arkusze
        xl_file = pd.ExcelFile(filename)
        print(f"Dostępne arkusze: {xl_file.sheet_names}")

        # Wczytaj arkusz "Detailed Report"
        if "Detailed Report" in xl_file.sheet_names:
            df = pd.read_excel(filename, sheet_name="Detailed Report")
        else:
            print("Nie znaleziono arkusza 'Detailed Report'. Używam pierwszego arkusza.")
            df = pd.read_excel(filename, sheet_name=0)

        print(f"Wczytano {len(df)} wierszy")
        print(f"Kolumny: {list(df.columns)}")

        # Znajdź kolumnę Simon
        simon_column = None
        for col in df.columns:
            if 'simon' in str(col).lower():
                simon_column = col
                break

        if simon_column is None:
            print("Nie znaleziono kolumny 'Simon'")
            return

        print(f"Znaleziono kolumnę Simon: {simon_column}")

        # KROK 1: Przeskanuj WSZYSTKIE wiersze (nie tylko z komentarzami Simona) aby znaleźć numery JIRA
        all_jira_keys_in_file = {}  # {jira_key: [lista_wierszy_gdzie_wystepuje]}

        for index, row in df.iterrows():
            # Pobierz wszystkie teksty z wiersza
            all_row_text = ""
            for col, value in row.items():
                if pd.notna(value) and isinstance(value, str):
                    all_row_text += f" {value}"

            jira_keys = extract_jira_keys(all_row_text)

            for jira_key in jira_keys:
                if jira_key not in all_jira_keys_in_file:
                    all_jira_keys_in_file[jira_key] = []
                all_jira_keys_in_file[jira_key].append(index + 1)  # Numer wiersza (1-based)

        print(f"Znaleziono {len(all_jira_keys_in_file)} unikalnych numerów JIRA: {list(all_jira_keys_in_file.keys())}")

        # KROK 2: Pobierz szczegóły WSZYSTKICH unikalnych zadań JIRA
        print("Pobieranie szczegółów wszystkich zadań JIRA...")
        all_jira_details = {}
        for jira_key in all_jira_keys_in_file.keys():
            print(f"Pobieranie {jira_key}...")
            jira_issue = get_jira_issue(jira_key)
            if jira_issue:
                all_jira_details[jira_key] = jira_issue.get('fields', {})

        print(f"Pobrano szczegóły {len(all_jira_details)} zadań JIRA")

        # KROK 3: Utwórz 3 nowe kolumny
        dane_zrodlowe_responses = []
        jira_ticket_responses = []
        analiza_jarka_responses = []

        for index, row in df.iterrows():
            simon_comment = row.get(simon_column, "")

            # Pobierz dodatkowe informacje z wiersza
            description_text = ""
            task_text = ""

            # Spróbuj znaleźć kolumny z opisem zadania
            for col in df.columns:
                col_lower = str(col).lower()
                if any(word in col_lower for word in ['description', 'task', 'project', 'opis', 'zadanie']):
                    if pd.notna(row.get(col)):
                        if 'description' in col_lower or 'opis' in col_lower:
                            description_text += str(row.get(col, "")) + " "
                        elif 'task' in col_lower or 'zadanie' in col_lower:
                            task_text += str(row.get(col, "")) + " "

            # Znajdź numery JIRA w tym wierszu
            all_row_text = f"{simon_comment} {description_text} {task_text}"
            for col, value in row.items():
                if pd.notna(value) and isinstance(value, str):
                    all_row_text += f" {value}"

            jira_keys_in_row = extract_jira_keys(all_row_text)

            # Generuj odpowiedzi dla 3 kolumn
            if pd.notna(simon_comment) and str(simon_comment).strip():
                print(f"Przetwarzanie wiersza {index + 1}: {str(simon_comment)[:50]}...")
                responses = analyze_row_with_jira(
                    simon_comment,
                    description_text,
                    task_text,
                    row_data=row.to_dict(),
                    jira_keys_in_row=jira_keys_in_row,
                    all_jira_keys_in_file=all_jira_keys_in_file,
                    all_jira_details=all_jira_details,
                    current_row_number=index + 1
                )
                dane_zrodlowe_responses.append(responses["dane_zrodlowe"])
                jira_ticket_responses.append(responses["jira_ticket"])
                analiza_jarka_responses.append(responses["analiza_jarka"])
            elif jira_keys_in_row:
                # Wiersz bez komentarza Simona, ale z numerami JIRA
                print(f"Wiersz {index + 1}: Brak komentarza Simona, ale znaleziono JIRA: {jira_keys_in_row}")
                responses = analyze_row_with_jira(
                    "",
                    description_text,
                    task_text,
                    row_data=row.to_dict(),
                    jira_keys_in_row=jira_keys_in_row,
                    all_jira_keys_in_file=all_jira_keys_in_file,
                    all_jira_details=all_jira_details,
                    current_row_number=index + 1
                )
                dane_zrodlowe_responses.append(responses["dane_zrodlowe"])
                jira_ticket_responses.append(responses["jira_ticket"])
                analiza_jarka_responses.append(responses["analiza_jarka"])
            else:
                dane_zrodlowe_responses.append("")
                jira_ticket_responses.append("")
                analiza_jarka_responses.append("")
        
        # Dodaj 3 nowe kolumny
        df['Dane źródłowe'] = dane_zrodlowe_responses
        df['JIRA Ticket'] = jira_ticket_responses
        df['Analiza Jarka'] = analiza_jarka_responses

        # Zapisz do nowego pliku
        output_filename = f"Clockify_3_kolumny_analiza_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        with pd.ExcelWriter(output_filename, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Detailed Report', index=False)

            # Dostosuj szerokość kolumn
            worksheet = writer.sheets['Detailed Report']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass

                # Specjalne szerokości dla nowych kolumn
                if column[0].value in ['Dane źródłowe', 'JIRA Ticket', 'Analiza Jarka']:
                    adjusted_width = min(max_length + 2, 80)  # Szersze kolumny dla analizy
                else:
                    adjusted_width = min(max_length + 2, 50)  # Standardowe kolumny

                worksheet.column_dimensions[column_letter].width = adjusted_width

        print(f"Plik został zapisany jako: {output_filename}")
        print(f"Dodano {len([r for r in dane_zrodlowe_responses if r])} wierszy z analizą")
        print("Utworzono 3 kolumny:")
        print("- 'Dane źródłowe': wszystkie dane użyte do analizy")
        print("- 'JIRA Ticket': szczegóły zadań z JIRA")
        print("- 'Analiza Jarka': interpretacja i uzasadnienie klasyfikacji")
        
        return output_filename
        
    except Exception as e:
        print(f"Błąd podczas przetwarzania pliku: {e}")
        return None

def main():
    """Główna funkcja"""
    print("Analiza komentarzy Simona i generowanie odpowiedzi Jarka...")
    
    output_file = process_clockify_file()
    
    if output_file:
        print(f"\n✅ Sukces! Plik został przetworzony: {output_file}")
        print("\n📊 Utworzono 3 kolumny analizy:")
        print("1. 'Dane źródłowe' - wszystkie dane użyte do oceny")
        print("2. 'JIRA Ticket' - szczegóły zadań z systemu JIRA")
        print("3. 'Analiza Jarka' - klasyfikacja Bug/Improvement z uzasadnieniem")
        print("\n🔍 Funkcjonalności:")
        print("- Automatyczne wykrywanie duplikatów JIRA z odnośnikami")
        print("- Parsowanie opisów JIRA z formatu JSON")
        print("- Szczegółowe uzasadnienie każdej klasyfikacji")
        print("- Rekomendacje działań dla każdego typu zagadnienia")
    else:
        print("\n❌ Wystąpił błąd podczas przetwarzania pliku")

if __name__ == "__main__":
    main()
