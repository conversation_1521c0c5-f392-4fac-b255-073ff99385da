#!/usr/bin/env py
# -*- coding: utf-8 -*-

import pandas as pd
import requests
import json
import base64
import re
from datetime import datetime

# Konfiguracja JIRA
JIRA_URL = "https://read-at-sea.atlassian.net"
EMAIL = "j.wik<PERSON><PERSON>@a-soft.pl"
API_TOKEN = "ATATT3xFfGF0o1tVy4he6mvThd2U0mZU2NCqHbZoCB216R6aPWZZUnKecNaRaTIFIBkmb3sTHDvEPtjnJ__aW1EGolFwrIFPuo5qY9Z4SEW2TlMMt8d8v0sLWKkGGMkjLSDQQyP2wT4BImUn_XaNGozE1-KzMj1GFxhAtdkdmwtMErw0hkwPo4Q=D1D6CC05"

def extract_text_from_jira_content(content):
    """Wyciąga tekst z struktury JIRA content (JSON)"""
    if not content:
        return ""

    if isinstance(content, str):
        return content

    if isinstance(content, dict):
        text_parts = []

        # Sprawdź czy to struktura Atlassian Document Format
        if content.get('type') == 'doc' and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))

        # Sprawdź inne typy elementów
        elif content.get('type') == 'paragraph' and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))

        elif content.get('type') == 'text' and 'text' in content:
            text_parts.append(content['text'])

        elif content.get('type') in ['heading', 'codeBlock', 'blockquote'] and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))

        # Jeśli to lista
        elif content.get('type') in ['bulletList', 'orderedList'] and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))

        elif content.get('type') == 'listItem' and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))

        return ' '.join(text_parts)

    elif isinstance(content, list):
        text_parts = []
        for item in content:
            text_parts.append(extract_text_from_jira_content(item))
        return ' '.join(text_parts)

    return str(content)

def get_jira_issue(issue_key):
    """Pobiera szczegóły zadania z JIRA"""

    # Przygotowanie autoryzacji
    auth_string = f"{EMAIL}:{API_TOKEN}"
    auth_bytes = auth_string.encode('ascii')
    auth_b64 = base64.b64encode(auth_bytes).decode('ascii')

    headers = {
        'Authorization': f'Basic {auth_b64}',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }

    url = f"{JIRA_URL}/rest/api/3/issue/{issue_key}"
    params = {
        'fields': 'key,summary,status,assignee,reporter,created,updated,priority,issuetype,description,components,labels'
    }

    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        data = response.json()

        # Przetwórz opis z formatu JIRA na tekst
        if data.get('fields', {}).get('description'):
            description_content = data['fields']['description']
            data['fields']['description_text'] = extract_text_from_jira_content(description_content)
            data['fields']['description_raw'] = description_content

        return data
    except requests.exceptions.RequestException as e:
        print(f"Błąd podczas pobierania zadania {issue_key}: {e}")
        return None

def extract_jira_keys(text):
    """Wyciąga numery JIRA z tekstu"""
    if pd.isna(text) or not isinstance(text, str):
        return []
    
    # Wzorce dla numerów JIRA (RS-123, RASEA-456, itp.)
    patterns = [
        r'\b(RS-\d+)\b',
        r'\b(RASEA-\d+)\b',
        r'\b([A-Z]+-\d+)\b'
    ]
    
    jira_keys = []
    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        jira_keys.extend(matches)
    
    return list(set(jira_keys))  # Usuń duplikaty

def analyze_simon_comment(simon_comment, description_text="", task_text="", row_data=None, processed_jira_keys=None, all_jira_keys_in_file=None, current_row_number=None):
    """Analizuje komentarz Simona i generuje 3 kolumny danych"""

    if pd.isna(simon_comment) or not isinstance(simon_comment, str):
        return {"dane_zrodlowe": "", "jira_ticket": "", "analiza_jarka": ""}

    if processed_jira_keys is None:
        processed_jira_keys = set()
    if all_jira_keys_in_file is None:
        all_jira_keys_in_file = {}

    # Znajdź numery JIRA w komentarzu, opisie lub nazwie zadania
    all_text = f"{simon_comment} {description_text} {task_text}"
    jira_keys = extract_jira_keys(all_text)

    # Sprawdź czy to są już przetworzone numery JIRA
    duplicate_jira_keys = []
    new_jira_keys = []

    for jira_key in jira_keys:
        if jira_key in processed_jira_keys:
            # To jest duplikat - znajdź pierwszy wiersz gdzie wystąpił
            first_occurrence_rows = all_jira_keys_in_file.get(jira_key, [])
            first_row = min([r for r in first_occurrence_rows if r < current_row_number]) if first_occurrence_rows else None
            duplicate_jira_keys.append((jira_key, first_row))
        else:
            new_jira_keys.append(jira_key)

    # Dodaj nowe klucze do zbioru przetworzonych
    processed_jira_keys.update(new_jira_keys)

    # KOLUMNA 1: DANE ŹRÓDŁOWE
    dane_zrodlowe = "**DANE ŹRÓDŁOWE UŻYTE DO ANALIZY:**\n\n"
    dane_zrodlowe += f"• **Komentarz Simona:** {simon_comment}\n"
    if description_text.strip():
        dane_zrodlowe += f"• **Opis zadania:** {description_text.strip()}\n"
    if task_text.strip():
        dane_zrodlowe += f"• **Nazwa zadania:** {task_text.strip()}\n"

    # Dodaj inne istotne dane z wiersza
    if row_data:
        for col, value in row_data.items():
            if pd.notna(value) and isinstance(value, str) and value.strip():
                col_lower = str(col).lower()
                if any(word in col_lower for word in ['project', 'client', 'category', 'tags']):
                    dane_zrodlowe += f"• **{col}:** {value}\n"

    if jira_keys:
        dane_zrodlowe += f"• **Znalezione numery JIRA:** {', '.join(jira_keys)}\n"

    # KOLUMNA 2: JIRA TICKET
    jira_ticket = ""
    jira_details = {}

    if duplicate_jira_keys and not new_jira_keys:
        references = []
        for jira_key, first_row in duplicate_jira_keys:
            if first_row:
                references.append(f"{jira_key} (patrz wiersz {first_row})")
            else:
                references.append(f"{jira_key} (patrz wyżej)")
        jira_ticket = f"**PATRZ WYŻEJ SZCZEGÓŁY** dla {', '.join(references)}"
    elif new_jira_keys:
        jira_ticket = "**SZCZEGÓŁY ZADAŃ Z JIRA:**\n\n"
        for jira_key in new_jira_keys:
            jira_issue = get_jira_issue(jira_key)
            if jira_issue:
                fields = jira_issue.get('fields', {})
                jira_details[jira_key] = fields

                jira_ticket += f"**{jira_key}**\n"
                jira_ticket += f"• **Tytuł:** {fields.get('summary', 'Brak tytułu')}\n"
                jira_ticket += f"• **Status:** {fields.get('status', {}).get('name', 'Nieznany')}\n"
                jira_ticket += f"• **Typ:** {fields.get('issuetype', {}).get('name', 'Nieznany')}\n"
                jira_ticket += f"• **Priorytet:** {fields.get('priority', {}).get('name', 'Nieznany')}\n"

                if fields.get('assignee'):
                    jira_ticket += f"• **Przypisany do:** {fields['assignee'].get('displayName', 'Nieznany')}\n"

                if fields.get('description_text'):
                    jira_ticket += f"• **Opis:** {fields['description_text']}\n"

                if fields.get('labels'):
                    jira_ticket += f"• **Etykiety:** {', '.join(fields['labels'])}\n"

                if fields.get('components'):
                    components = [comp.get('name', '') for comp in fields['components']]
                    jira_ticket += f"• **Komponenty:** {', '.join(components)}\n"

                jira_ticket += "\n"
            else:
                jira_ticket += f"**{jira_key}** - Nie udało się pobrać szczegółów\n\n"

    # KOLUMNA 3: ANALIZA JARKA
    if duplicate_jira_keys and not new_jira_keys:
        references = []
        for jira_key, first_row in duplicate_jira_keys:
            if first_row:
                references.append(f"{jira_key} (patrz wiersz {first_row})")
            else:
                references.append(f"{jira_key} (patrz wyżej)")
        analiza_jarka = f"**PATRZ WYŻEJ ANALIZĘ** dla {', '.join(references)}"
    else:
        # Zaawansowana analiza typu na podstawie wszystkich dostępnych danych
        classification = classify_issue_type(simon_comment, description_text, task_text, row_data, jira_details)

        analiza_jarka = f"{classification['header']}\n\n"
        analiza_jarka += f"**UZASADNIENIE KLASYFIKACJI:**\n{classification['explanation']}\n\n"
        analiza_jarka += f"**REKOMENDOWANE DZIAŁANIA:**\n{classification['recommendations']}"

    return {
        "dane_zrodlowe": dane_zrodlowe,
        "jira_ticket": jira_ticket,
        "analiza_jarka": analiza_jarka
    }

def classify_issue_type(simon_comment, description_text, task_text, row_data, jira_details):
    """Klasyfikuje typ zagadnienia na podstawie wszystkich dostępnych danych"""

    # Połącz wszystkie teksty do analizy
    all_text = f"{simon_comment} {description_text} {task_text}".lower()

    # Dodaj dane z wiersza jeśli dostępne
    if row_data is not None:
        for col, value in row_data.items():
            if pd.notna(value) and isinstance(value, str):
                all_text += f" {value}".lower()

    # Dodaj dane z JIRA
    jira_text = ""
    jira_types = []
    jira_descriptions = []
    for jira_key, fields in jira_details.items():
        if fields.get('summary'):
            jira_text += f" {fields['summary']}".lower()
        if fields.get('description_text'):
            jira_text += f" {fields['description_text']}".lower()
            jira_descriptions.append(fields['description_text'])
        if fields.get('issuetype', {}).get('name'):
            jira_types.append(fields['issuetype']['name'].lower())
        if fields.get('labels'):
            for label in fields['labels']:
                jira_text += f" {label}".lower()

    all_text += jira_text

    # Słowa kluczowe dla różnych kategorii
    bug_keywords = [
        'bug', 'błąd', 'error', 'problem', 'issue', 'defect', 'crash', 'fail', 'broken',
        'not working', 'nie działa', 'exception', 'null', 'undefined', 'incorrect',
        'wrong', 'fix', 'repair', 'solve', 'resolve', 'debug', 'malfunction', 'fault'
    ]

    improvement_keywords = [
        'improvement', 'enhance', 'feature', 'add', 'improve', 'usprawnienie', 'funkcja',
        'new', 'better', 'optimize', 'performance', 'upgrade', 'extend', 'modify',
        'change', 'update', 'refactor', 'redesign', 'implement', 'dodawanie', 'dodanie',
        'tworzenie', 'utworzenie', 'rozszerzenie', 'parametrów', 'parametry', 'funkcjonalność'
    ]

    question_keywords = [
        'question', 'pytanie', 'how', 'why', 'what', 'jak', 'dlaczego', 'co',
        'explain', 'clarify', 'understand', 'meaning', 'purpose'
    ]

    test_keywords = [
        'test', 'testing', 'verify', 'check', 'sprawdź', 'validate', 'confirm',
        'review', 'qa', 'quality', 'acceptance'
    ]

    # Liczenie wystąpień słów kluczowych
    bug_score = sum(1 for word in bug_keywords if word in all_text)
    improvement_score = sum(1 for word in improvement_keywords if word in all_text)
    question_score = sum(1 for word in question_keywords if word in all_text)
    test_score = sum(1 for word in test_keywords if word in all_text)

    # Dodatkowe punkty na podstawie typu w JIRA
    if any('bug' in jtype for jtype in jira_types):
        bug_score += 3
    if any('story' in jtype or 'feature' in jtype or 'improvement' in jtype for jtype in jira_types):
        improvement_score += 3
    if any('task' in jtype for jtype in jira_types):
        improvement_score += 1

    # Określ typ na podstawie najwyższego wyniku
    scores = {
        'bug': bug_score,
        'improvement': improvement_score,
        'question': question_score,
        'test': test_score
    }

    max_score = max(scores.values())
    if max_score == 0:
        issue_type = 'general'
    else:
        issue_type = max(scores, key=scores.get)

    # Przygotuj odpowiedź
    if issue_type == 'bug':
        header = "🐛 **BUG** - "
        explanation = f"Sklasyfikowane jako BUG na podstawie:\n"
        explanation += f"- Słowa kluczowe wskazujące na błąd (wynik: {bug_score})\n"
        if any('bug' in jtype for jtype in jira_types):
            explanation += f"- Typ zadania w JIRA: {', '.join(jira_types)}\n"
        if jira_descriptions:
            explanation += f"- Opis z JIRA: '{' | '.join(jira_descriptions[:2])}'\n"
        explanation += f"- Kontekst wskazuje na nieprawidłowe działanie systemu\n"
        explanation += f"- Wymaga naprawy istniejącej funkcjonalności"

        recommendations = """1. Zreprodukować błąd w środowisku testowym
2. Zidentyfikować przyczynę źródłową (root cause analysis)
3. Opracować i przetestować poprawkę
4. Przeprowadzić testy regresyjne
5. Wdrożyć na produkcję z odpowiednimi testami"""

    elif issue_type == 'improvement':
        header = "✨ **IMPROVEMENT** - "
        explanation = f"Sklasyfikowane jako IMPROVEMENT na podstawie:\n"
        explanation += f"- Słowa kluczowe wskazujące na usprawnienie (wynik: {improvement_score})\n"
        if any('story' in jtype or 'feature' in jtype or 'improvement' in jtype for jtype in jira_types):
            explanation += f"- Typ zadania w JIRA: {', '.join(jira_types)}\n"
        if jira_descriptions:
            explanation += f"- Opis z JIRA: '{' | '.join(jira_descriptions[:2])}'\n"
        explanation += f"- Kontekst wskazuje na dodanie nowej funkcjonalności\n"
        explanation += f"- Dotyczy rozszerzenia lub poprawy istniejących możliwości"

        recommendations = """1. Przeanalizować wpływ na istniejącą funkcjonalność
2. Oszacować nakład pracy i zasoby
3. Zaplanować implementację w odpowiednim sprincie
4. Przygotować testy akceptacyjne i dokumentację
5. Skonsultować z zespołem UX/UI jeśli potrzeba"""

    elif issue_type == 'question':
        header = "❓ **PYTANIE** - "
        explanation = f"Sklasyfikowane jako PYTANIE na podstawie:\n"
        explanation += f"- Słowa kluczowe wskazujące na pytanie (wynik: {question_score})\n"
        explanation += f"- Kontekst wymaga wyjaśnienia lub dodatkowych informacji\n"
        explanation += f"- Potrzebne jest zrozumienie wymagań lub funkcjonalności"

        recommendations = """1. Przeanalizować szczegółowo pytanie
2. Zebrać dodatkowe informacje jeśli potrzeba
3. Skonsultować z zespołem lub ekspertami
4. Przygotować szczegółową odpowiedź
5. Udokumentować rozwiązanie dla przyszłości"""

    elif issue_type == 'test':
        header = "🧪 **TESTOWANIE** - "
        explanation = f"Sklasyfikowane jako TESTOWANIE na podstawie:\n"
        explanation += f"- Słowa kluczowe wskazujące na testy (wynik: {test_score})\n"
        explanation += f"- Kontekst wymaga weryfikacji lub sprawdzenia\n"
        explanation += f"- Dotyczy kontroli jakości lub akceptacji"

        recommendations = """1. Przygotować plan testów
2. Wykonać testy funkcjonalne i niefunkcjonalne
3. Udokumentować wyniki testów
4. Zgłosić ewentualne błędy lub problemy
5. Potwierdzić akceptację funkcjonalności"""

    else:
        header = "📝 **UWAGA** - "
        explanation = f"Wymaga dodatkowej analizy:\n"
        explanation += f"- Brak jednoznacznych wskaźników typu (bug: {bug_score}, improvement: {improvement_score})\n"
        explanation += f"- Kontekst wymaga głębszej analizy\n"
        explanation += f"- Może wymagać konsultacji z zespołem"

        recommendations = """1. Przeanalizować szczegółowo kontekst
2. Skonsultować z zespołem lub klientem
3. Określić priorytet i typ działania
4. Zaplanować odpowiednie kroki
5. Monitorować postęp i wyniki"""

    return {
        'header': header,
        'explanation': explanation,
        'recommendations': recommendations,
        'type': issue_type,
        'scores': scores
    }

def process_clockify_file():
    """Przetwarza plik Clockify i dodaje kolumnę z odpowiedziami Jarka"""
    
    filename = "clockify_report.xlsx"
    
    try:
        # Wczytaj plik Excel
        print(f"Wczytywanie pliku: {filename}")
        
        # Sprawdź dostępne arkusze
        xl_file = pd.ExcelFile(filename)
        print(f"Dostępne arkusze: {xl_file.sheet_names}")
        
        # Wczytaj arkusz "Detailed Report"
        if "Detailed Report" in xl_file.sheet_names:
            df = pd.read_excel(filename, sheet_name="Detailed Report")
        else:
            print("Nie znaleziono arkusza 'Detailed Report'. Używam pierwszego arkusza.")
            df = pd.read_excel(filename, sheet_name=0)
        
        print(f"Wczytano {len(df)} wierszy")
        print(f"Kolumny: {list(df.columns)}")
        
        # Znajdź kolumnę Simon
        simon_column = None
        for col in df.columns:
            if 'simon' in str(col).lower():
                simon_column = col
                break
        
        if simon_column is None:
            print("Nie znaleziono kolumny 'Simon'")
            return
        
        print(f"Znaleziono kolumnę Simon: {simon_column}")
        
        # Najpierw przeskanuj wszystkie wiersze, aby znaleźć wszystkie numery JIRA
        all_jira_keys_in_file = {}  # {jira_key: [lista_wierszy_gdzie_wystepuje]}

        for index, row in df.iterrows():
            simon_comment = row.get(simon_column, "")
            if pd.notna(simon_comment) and str(simon_comment).strip():
                # Pobierz dodatkowe informacje z wiersza
                description_text = ""
                task_text = ""
                for col in df.columns:
                    col_lower = str(col).lower()
                    if any(word in col_lower for word in ['description', 'task', 'project', 'opis', 'zadanie']):
                        if pd.notna(row.get(col)):
                            if 'description' in col_lower or 'opis' in col_lower:
                                description_text += str(row.get(col, "")) + " "
                            elif 'task' in col_lower or 'zadanie' in col_lower:
                                task_text += str(row.get(col, "")) + " "

                all_text = f"{simon_comment} {description_text} {task_text}"
                jira_keys = extract_jira_keys(all_text)

                for jira_key in jira_keys:
                    if jira_key not in all_jira_keys_in_file:
                        all_jira_keys_in_file[jira_key] = []
                    all_jira_keys_in_file[jira_key].append(index + 1)  # Numer wiersza (1-based)

        print(f"Znaleziono numery JIRA w pliku: {list(all_jira_keys_in_file.keys())}")

        # Utwórz 3 nowe kolumny
        dane_zrodlowe_responses = []
        jira_ticket_responses = []
        analiza_jarka_responses = []
        processed_jira_keys = set()  # Śledzenie przetworzonych kluczy JIRA

        for index, row in df.iterrows():
            simon_comment = row.get(simon_column, "")

            # Pobierz dodatkowe informacje z wiersza
            description_text = ""
            task_text = ""

            # Spróbuj znaleźć kolumny z opisem zadania
            for col in df.columns:
                col_lower = str(col).lower()
                if any(word in col_lower for word in ['description', 'task', 'project', 'opis', 'zadanie']):
                    if pd.notna(row.get(col)):
                        if 'description' in col_lower or 'opis' in col_lower:
                            description_text += str(row.get(col, "")) + " "
                        elif 'task' in col_lower or 'zadanie' in col_lower:
                            task_text += str(row.get(col, "")) + " "

            # Generuj odpowiedzi dla 3 kolumn
            if pd.notna(simon_comment) and str(simon_comment).strip():
                print(f"Przetwarzanie wiersza {index + 1}: {str(simon_comment)[:50]}...")
                responses = analyze_simon_comment(
                    simon_comment,
                    description_text,
                    task_text,
                    row_data=row.to_dict(),
                    processed_jira_keys=processed_jira_keys,
                    all_jira_keys_in_file=all_jira_keys_in_file,
                    current_row_number=index + 1
                )
                dane_zrodlowe_responses.append(responses["dane_zrodlowe"])
                jira_ticket_responses.append(responses["jira_ticket"])
                analiza_jarka_responses.append(responses["analiza_jarka"])
            else:
                dane_zrodlowe_responses.append("")
                jira_ticket_responses.append("")
                analiza_jarka_responses.append("")
        
        # Dodaj 3 nowe kolumny
        df['Dane źródłowe'] = dane_zrodlowe_responses
        df['JIRA Ticket'] = jira_ticket_responses
        df['Analiza Jarka'] = analiza_jarka_responses

        # Zapisz do nowego pliku
        output_filename = f"Clockify_3_kolumny_analiza_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        with pd.ExcelWriter(output_filename, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Detailed Report', index=False)

            # Dostosuj szerokość kolumn
            worksheet = writer.sheets['Detailed Report']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass

                # Specjalne szerokości dla nowych kolumn
                if column[0].value in ['Dane źródłowe', 'JIRA Ticket', 'Analiza Jarka']:
                    adjusted_width = min(max_length + 2, 80)  # Szersze kolumny dla analizy
                else:
                    adjusted_width = min(max_length + 2, 50)  # Standardowe kolumny

                worksheet.column_dimensions[column_letter].width = adjusted_width

        print(f"Plik został zapisany jako: {output_filename}")
        print(f"Dodano {len([r for r in dane_zrodlowe_responses if r])} wierszy z analizą")
        print("Utworzono 3 kolumny:")
        print("- 'Dane źródłowe': wszystkie dane użyte do analizy")
        print("- 'JIRA Ticket': szczegóły zadań z JIRA")
        print("- 'Analiza Jarka': interpretacja i uzasadnienie klasyfikacji")
        
        return output_filename
        
    except Exception as e:
        print(f"Błąd podczas przetwarzania pliku: {e}")
        return None

def main():
    """Główna funkcja"""
    print("Analiza komentarzy Simona i generowanie odpowiedzi Jarka...")
    
    output_file = process_clockify_file()
    
    if output_file:
        print(f"\n✅ Sukces! Plik został przetworzony: {output_file}")
        print("\n📊 Utworzono 3 kolumny analizy:")
        print("1. 'Dane źródłowe' - wszystkie dane użyte do oceny")
        print("2. 'JIRA Ticket' - szczegóły zadań z systemu JIRA")
        print("3. 'Analiza Jarka' - klasyfikacja Bug/Improvement z uzasadnieniem")
        print("\n🔍 Funkcjonalności:")
        print("- Automatyczne wykrywanie duplikatów JIRA z odnośnikami")
        print("- Parsowanie opisów JIRA z formatu JSON")
        print("- Szczegółowe uzasadnienie każdej klasyfikacji")
        print("- Rekomendacje działań dla każdego typu zagadnienia")
    else:
        print("\n❌ Wystąpił błąd podczas przetwarzania pliku")

if __name__ == "__main__":
    main()
