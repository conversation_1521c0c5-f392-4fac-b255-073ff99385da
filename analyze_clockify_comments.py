#!/usr/bin/env py
# -*- coding: utf-8 -*-

import pandas as pd
import requests
import json
import base64
import re
from datetime import datetime

# Konfiguracja JIRA
JIRA_URL = "https://read-at-sea.atlassian.net"
EMAIL = "j.wik<PERSON><PERSON>@a-soft.pl"
API_TOKEN = "ATATT3xFfGF0o1tVy4he6mvThd2U0mZU2NCqHbZoCB216R6aPWZZUnKecNaRaTIFIBkmb3sTHDvEPtjnJ__aW1EGolFwrIFPuo5qY9Z4SEW2TlMMt8d8v0sLWKkGGMkjLSDQQyP2wT4BImUn_XaNGozE1-KzMj1GFxhAtdkdmwtMErw0hkwPo4Q=D1D6CC05"

def get_jira_issue(issue_key):
    """Pobiera szczegóły zadania z JIRA"""
    
    # Przygotowanie autoryzacji
    auth_string = f"{EMAIL}:{API_TOKEN}"
    auth_bytes = auth_string.encode('ascii')
    auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
    
    headers = {
        'Authorization': f'Basic {auth_b64}',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }
    
    url = f"{JIRA_URL}/rest/api/3/issue/{issue_key}"
    params = {
        'fields': 'key,summary,status,assignee,reporter,created,updated,priority,issuetype,description,components'
    }
    
    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Błąd podczas pobierania zadania {issue_key}: {e}")
        return None

def extract_jira_keys(text):
    """Wyciąga numery JIRA z tekstu"""
    if pd.isna(text) or not isinstance(text, str):
        return []
    
    # Wzorce dla numerów JIRA (RS-123, RASEA-456, itp.)
    patterns = [
        r'\b(RS-\d+)\b',
        r'\b(RASEA-\d+)\b',
        r'\b([A-Z]+-\d+)\b'
    ]
    
    jira_keys = []
    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        jira_keys.extend(matches)
    
    return list(set(jira_keys))  # Usuń duplikaty

def analyze_simon_comment(simon_comment, description_text="", task_text=""):
    """Analizuje komentarz Simona i generuje odpowiedź Jarka"""
    
    if pd.isna(simon_comment) or not isinstance(simon_comment, str):
        return ""
    
    simon_lower = simon_comment.lower()
    
    # Znajdź numery JIRA w komentarzu, opisie lub nazwie zadania
    all_text = f"{simon_comment} {description_text} {task_text}"
    jira_keys = extract_jira_keys(all_text)
    
    response = ""
    
    # Analiza typu pytania/komentarza
    if any(word in simon_lower for word in ['bug', 'błąd', 'error', 'problem', 'issue', 'defect']):
        response += "🐛 **BUG** - "
        if jira_keys:
            response += f"Sprawdzę szczegóły w {', '.join(jira_keys)}. "
        response += "Wymaga analizy przyczyny błędu i naprawy. "
        
    elif any(word in simon_lower for word in ['improvement', 'enhance', 'feature', 'add', 'improve', 'usprawnienie', 'funkcja']):
        response += "✨ **IMPROVEMENT** - "
        if jira_keys:
            response += f"Odnoszę się do {', '.join(jira_keys)}. "
        response += "Propozycja usprawnienia do rozważenia. "
        
    elif any(word in simon_lower for word in ['question', 'pytanie', 'how', 'why', 'what', 'jak', 'dlaczego', 'co']):
        response += "❓ **PYTANIE** - "
        if jira_keys:
            response += f"Dotyczy {', '.join(jira_keys)}. "
        response += "Wymaga wyjaśnienia lub dodatkowych informacji. "
        
    elif any(word in simon_lower for word in ['test', 'testing', 'verify', 'check', 'sprawdź', 'test']):
        response += "🧪 **TESTOWANIE** - "
        if jira_keys:
            response += f"Związane z {', '.join(jira_keys)}. "
        response += "Wymaga weryfikacji lub testów. "
        
    else:
        response += "📝 **UWAGA** - "
        if jira_keys:
            response += f"Dotyczy {', '.join(jira_keys)}. "
        response += "Wymaga przeanalizowania. "
    
    # Dodaj szczegóły z JIRA jeśli są dostępne numery
    if jira_keys:
        response += "\n\n**Szczegóły z JIRA:**\n"
        for jira_key in jira_keys[:3]:  # Maksymalnie 3 zadania
            jira_issue = get_jira_issue(jira_key)
            if jira_issue:
                fields = jira_issue.get('fields', {})
                response += f"• **{jira_key}**: {fields.get('summary', 'Brak tytułu')}\n"
                response += f"  - Status: {fields.get('status', {}).get('name', 'Nieznany')}\n"
                response += f"  - Typ: {fields.get('issuetype', {}).get('name', 'Nieznany')}\n"
                if fields.get('description'):
                    desc = fields['description'][:100] + "..." if len(fields.get('description', '')) > 100 else fields.get('description', '')
                    response += f"  - Opis: {desc}\n"
                response += "\n"
    
    # Dodaj rekomendacje
    response += "\n**Rekomendacje:**\n"
    
    if 'bug' in simon_lower or 'błąd' in simon_lower:
        response += "1. Zreprodukować błąd w środowisku testowym\n"
        response += "2. Zidentyfikować przyczynę źródłową\n"
        response += "3. Opracować i przetestować poprawkę\n"
        response += "4. Wdrożyć na produkcję z odpowiednimi testami\n"
    elif 'improvement' in simon_lower or 'usprawnienie' in simon_lower:
        response += "1. Przeanalizować wpływ na istniejącą funkcjonalność\n"
        response += "2. Oszacować nakład pracy\n"
        response += "3. Zaplanować implementację w odpowiednim sprincie\n"
        response += "4. Przygotować testy akceptacyjne\n"
    else:
        response += "1. Przeanalizować szczegółowo komentarz\n"
        response += "2. Skonsultować z zespołem jeśli potrzeba\n"
        response += "3. Zaplanować odpowiednie działania\n"
        response += "4. Monitorować postęp\n"
    
    return response

def process_clockify_file():
    """Przetwarza plik Clockify i dodaje kolumnę z odpowiedziami Jarka"""
    
    filename = "clockify_report.xlsx"
    
    try:
        # Wczytaj plik Excel
        print(f"Wczytywanie pliku: {filename}")
        
        # Sprawdź dostępne arkusze
        xl_file = pd.ExcelFile(filename)
        print(f"Dostępne arkusze: {xl_file.sheet_names}")
        
        # Wczytaj arkusz "Detailed Report"
        if "Detailed Report" in xl_file.sheet_names:
            df = pd.read_excel(filename, sheet_name="Detailed Report")
        else:
            print("Nie znaleziono arkusza 'Detailed Report'. Używam pierwszego arkusza.")
            df = pd.read_excel(filename, sheet_name=0)
        
        print(f"Wczytano {len(df)} wierszy")
        print(f"Kolumny: {list(df.columns)}")
        
        # Znajdź kolumnę Simon
        simon_column = None
        for col in df.columns:
            if 'simon' in str(col).lower():
                simon_column = col
                break
        
        if simon_column is None:
            print("Nie znaleziono kolumny 'Simon'")
            return
        
        print(f"Znaleziono kolumnę Simon: {simon_column}")
        
        # Utwórz kolumnę Jarek
        jarek_responses = []
        
        for index, row in df.iterrows():
            simon_comment = row.get(simon_column, "")
            
            # Pobierz dodatkowe informacje z wiersza
            description_text = ""
            task_text = ""
            
            # Spróbuj znaleźć kolumny z opisem zadania
            for col in df.columns:
                col_lower = str(col).lower()
                if any(word in col_lower for word in ['description', 'task', 'project', 'opis', 'zadanie']):
                    if pd.notna(row.get(col)):
                        if 'description' in col_lower or 'opis' in col_lower:
                            description_text += str(row.get(col, "")) + " "
                        elif 'task' in col_lower or 'zadanie' in col_lower:
                            task_text += str(row.get(col, "")) + " "
            
            # Generuj odpowiedź
            if pd.notna(simon_comment) and str(simon_comment).strip():
                print(f"Przetwarzanie wiersza {index + 1}: {str(simon_comment)[:50]}...")
                response = analyze_simon_comment(simon_comment, description_text, task_text)
            else:
                response = ""
            
            jarek_responses.append(response)
        
        # Dodaj kolumnę Jarek
        df['Jarek'] = jarek_responses
        
        # Zapisz do nowego pliku
        output_filename = f"Clockify_with_Jarek_responses_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        with pd.ExcelWriter(output_filename, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Detailed Report', index=False)
            
            # Dostosuj szerokość kolumn
            worksheet = writer.sheets['Detailed Report']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 100)  # Maksymalna szerokość 100
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        print(f"Plik został zapisany jako: {output_filename}")
        print(f"Dodano {len([r for r in jarek_responses if r])} odpowiedzi Jarka")
        
        return output_filename
        
    except Exception as e:
        print(f"Błąd podczas przetwarzania pliku: {e}")
        return None

def main():
    """Główna funkcja"""
    print("Analiza komentarzy Simona i generowanie odpowiedzi Jarka...")
    
    output_file = process_clockify_file()
    
    if output_file:
        print(f"\n✅ Sukces! Plik został przetworzony: {output_file}")
        print("\nKolumna 'Jarek' zawiera:")
        print("- Analizę typu komentarza (Bug/Improvement/Pytanie/etc.)")
        print("- Szczegóły z JIRA dla znalezionych numerów zadań")
        print("- Rekomendacje działań")
    else:
        print("\n❌ Wystąpił błąd podczas przetwarzania pliku")

if __name__ == "__main__":
    main()
