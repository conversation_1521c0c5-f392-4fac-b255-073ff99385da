#!/usr/bin/env py
# -*- coding: utf-8 -*-

import pandas as pd
import requests
import json
import base64
import re
from datetime import datetime

# Konfiguracja JIRA
JIRA_URL = "https://read-at-sea.atlassian.net"
EMAIL = "j.wik<PERSON><PERSON>@a-soft.pl"
API_TOKEN = "ATATT3xFfGF0o1tVy4he6mvThd2U0mZU2NCqHbZoCB216R6aPWZZUnKecNaRaTIFIBkmb3sTHDvEPtjnJ__aW1EGolFwrIFPuo5qY9Z4SEW2TlMMt8d8v0sLWKkGGMkjLSDQQyP2wT4BImUn_XaNGozE1-KzMj1GFxhAtdkdmwtMErw0hkwPo4Q=D1D6CC05"

def get_jira_issue(issue_key):
    """Pobiera szczegóły zadania z JIRA"""
    
    # Przygotowanie autoryzacji
    auth_string = f"{EMAIL}:{API_TOKEN}"
    auth_bytes = auth_string.encode('ascii')
    auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
    
    headers = {
        'Authorization': f'Basic {auth_b64}',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }
    
    url = f"{JIRA_URL}/rest/api/3/issue/{issue_key}"
    params = {
        'fields': 'key,summary,status,assignee,reporter,created,updated,priority,issuetype,description,components'
    }
    
    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Błąd podczas pobierania zadania {issue_key}: {e}")
        return None

def extract_jira_keys(text):
    """Wyciąga numery JIRA z tekstu"""
    if pd.isna(text) or not isinstance(text, str):
        return []
    
    # Wzorce dla numerów JIRA (RS-123, RASEA-456, itp.)
    patterns = [
        r'\b(RS-\d+)\b',
        r'\b(RASEA-\d+)\b',
        r'\b([A-Z]+-\d+)\b'
    ]
    
    jira_keys = []
    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        jira_keys.extend(matches)
    
    return list(set(jira_keys))  # Usuń duplikaty

def analyze_simon_comment(simon_comment, description_text="", task_text="", row_data=None, processed_jira_keys=None):
    """Analizuje komentarz Simona i generuje odpowiedź Jarka"""

    if pd.isna(simon_comment) or not isinstance(simon_comment, str):
        return ""

    if processed_jira_keys is None:
        processed_jira_keys = set()

    simon_lower = simon_comment.lower()

    # Znajdź numery JIRA w komentarzu, opisie lub nazwie zadania
    all_text = f"{simon_comment} {description_text} {task_text}"
    jira_keys = extract_jira_keys(all_text)

    # Sprawdź czy to są już przetworzone numery JIRA
    duplicate_jira_keys = [key for key in jira_keys if key in processed_jira_keys]
    new_jira_keys = [key for key in jira_keys if key not in processed_jira_keys]

    # Dodaj nowe klucze do zbioru przetworzonych
    processed_jira_keys.update(new_jira_keys)

    response = ""

    # Jeśli są tylko duplikaty, dodaj odniesienie
    if duplicate_jira_keys and not new_jira_keys:
        response += f"**PATRZ WYŻEJ WYJAŚNIENIE** dla {', '.join(duplicate_jira_keys)}\n\n"

    # Pobierz szczegóły z JIRA dla nowych kluczy
    jira_details = {}
    for jira_key in new_jira_keys:
        jira_issue = get_jira_issue(jira_key)
        if jira_issue:
            jira_details[jira_key] = jira_issue.get('fields', {})

    # Zaawansowana analiza typu na podstawie wszystkich dostępnych danych
    classification = classify_issue_type(simon_comment, description_text, task_text, row_data, jira_details)

    response += classification['header']

    # Dodaj szczegółowe wyjaśnienie klasyfikacji
    if classification['explanation']:
        response += f"\n\n**WYJAŚNIENIE KLASYFIKACJI:**\n{classification['explanation']}\n"

    # Dodaj szczegóły z JIRA dla nowych kluczy
    if new_jira_keys and jira_details:
        response += "\n**Szczegóły z JIRA:**\n"
        for jira_key in new_jira_keys:
            if jira_key in jira_details:
                fields = jira_details[jira_key]
                response += f"• **{jira_key}**: {fields.get('summary', 'Brak tytułu')}\n"
                response += f"  - Status: {fields.get('status', {}).get('name', 'Nieznany')}\n"
                response += f"  - Typ w JIRA: {fields.get('issuetype', {}).get('name', 'Nieznany')}\n"
                response += f"  - Priorytet: {fields.get('priority', {}).get('name', 'Nieznany')}\n"
                if fields.get('description'):
                    desc = fields['description'][:150] + "..." if len(fields.get('description', '')) > 150 else fields.get('description', '')
                    response += f"  - Opis: {desc}\n"
                response += "\n"

    # Dodaj rekomendacje na podstawie klasyfikacji
    response += f"\n**Rekomendacje:**\n{classification['recommendations']}"

    return response

def classify_issue_type(simon_comment, description_text, task_text, row_data, jira_details):
    """Klasyfikuje typ zagadnienia na podstawie wszystkich dostępnych danych"""

    # Połącz wszystkie teksty do analizy
    all_text = f"{simon_comment} {description_text} {task_text}".lower()

    # Dodaj dane z wiersza jeśli dostępne
    if row_data is not None:
        for col, value in row_data.items():
            if pd.notna(value) and isinstance(value, str):
                all_text += f" {value}".lower()

    # Dodaj dane z JIRA
    jira_text = ""
    jira_types = []
    for jira_key, fields in jira_details.items():
        if fields.get('summary'):
            jira_text += f" {fields['summary']}".lower()
        if fields.get('description'):
            jira_text += f" {fields['description']}".lower()
        if fields.get('issuetype', {}).get('name'):
            jira_types.append(fields['issuetype']['name'].lower())

    all_text += jira_text

    # Słowa kluczowe dla różnych kategorii
    bug_keywords = [
        'bug', 'błąd', 'error', 'problem', 'issue', 'defect', 'crash', 'fail', 'broken',
        'not working', 'nie działa', 'exception', 'null', 'undefined', 'incorrect',
        'wrong', 'fix', 'repair', 'solve', 'resolve', 'debug'
    ]

    improvement_keywords = [
        'improvement', 'enhance', 'feature', 'add', 'improve', 'usprawnienie', 'funkcja',
        'new', 'better', 'optimize', 'performance', 'upgrade', 'extend', 'modify',
        'change', 'update', 'refactor', 'redesign', 'implement'
    ]

    question_keywords = [
        'question', 'pytanie', 'how', 'why', 'what', 'jak', 'dlaczego', 'co',
        'explain', 'clarify', 'understand', 'meaning', 'purpose'
    ]

    test_keywords = [
        'test', 'testing', 'verify', 'check', 'sprawdź', 'validate', 'confirm',
        'review', 'qa', 'quality', 'acceptance'
    ]

    # Liczenie wystąpień słów kluczowych
    bug_score = sum(1 for word in bug_keywords if word in all_text)
    improvement_score = sum(1 for word in improvement_keywords if word in all_text)
    question_score = sum(1 for word in question_keywords if word in all_text)
    test_score = sum(1 for word in test_keywords if word in all_text)

    # Dodatkowe punkty na podstawie typu w JIRA
    if any('bug' in jtype for jtype in jira_types):
        bug_score += 3
    if any('story' in jtype or 'feature' in jtype or 'improvement' in jtype for jtype in jira_types):
        improvement_score += 3
    if any('task' in jtype for jtype in jira_types):
        improvement_score += 1

    # Określ typ na podstawie najwyższego wyniku
    scores = {
        'bug': bug_score,
        'improvement': improvement_score,
        'question': question_score,
        'test': test_score
    }

    max_score = max(scores.values())
    if max_score == 0:
        issue_type = 'general'
    else:
        issue_type = max(scores, key=scores.get)

    # Przygotuj odpowiedź
    if issue_type == 'bug':
        header = "🐛 **BUG** - "
        explanation = f"Sklasyfikowane jako BUG na podstawie:\n"
        explanation += f"- Słowa kluczowe wskazujące na błąd (wynik: {bug_score})\n"
        if any('bug' in jtype for jtype in jira_types):
            explanation += f"- Typ zadania w JIRA: {', '.join(jira_types)}\n"
        explanation += f"- Kontekst wskazuje na nieprawidłowe działanie systemu\n"
        explanation += f"- Wymaga naprawy istniejącej funkcjonalności"

        recommendations = """1. Zreprodukować błąd w środowisku testowym
2. Zidentyfikować przyczynę źródłową (root cause analysis)
3. Opracować i przetestować poprawkę
4. Przeprowadzić testy regresyjne
5. Wdrożyć na produkcję z odpowiednimi testami"""

    elif issue_type == 'improvement':
        header = "✨ **IMPROVEMENT** - "
        explanation = f"Sklasyfikowane jako IMPROVEMENT na podstawie:\n"
        explanation += f"- Słowa kluczowe wskazujące na usprawnienie (wynik: {improvement_score})\n"
        if any('story' in jtype or 'feature' in jtype for jtype in jira_types):
            explanation += f"- Typ zadania w JIRA: {', '.join(jira_types)}\n"
        explanation += f"- Kontekst wskazuje na dodanie nowej funkcjonalności\n"
        explanation += f"- Dotyczy rozszerzenia lub poprawy istniejących możliwości"

        recommendations = """1. Przeanalizować wpływ na istniejącą funkcjonalność
2. Oszacować nakład pracy i zasoby
3. Zaplanować implementację w odpowiednim sprincie
4. Przygotować testy akceptacyjne i dokumentację
5. Skonsultować z zespołem UX/UI jeśli potrzeba"""

    elif issue_type == 'question':
        header = "❓ **PYTANIE** - "
        explanation = f"Sklasyfikowane jako PYTANIE na podstawie:\n"
        explanation += f"- Słowa kluczowe wskazujące na pytanie (wynik: {question_score})\n"
        explanation += f"- Kontekst wymaga wyjaśnienia lub dodatkowych informacji\n"
        explanation += f"- Potrzebne jest zrozumienie wymagań lub funkcjonalności"

        recommendations = """1. Przeanalizować szczegółowo pytanie
2. Zebrać dodatkowe informacje jeśli potrzeba
3. Skonsultować z zespołem lub ekspertami
4. Przygotować szczegółową odpowiedź
5. Udokumentować rozwiązanie dla przyszłości"""

    elif issue_type == 'test':
        header = "🧪 **TESTOWANIE** - "
        explanation = f"Sklasyfikowane jako TESTOWANIE na podstawie:\n"
        explanation += f"- Słowa kluczowe wskazujące na testy (wynik: {test_score})\n"
        explanation += f"- Kontekst wymaga weryfikacji lub sprawdzenia\n"
        explanation += f"- Dotyczy kontroli jakości lub akceptacji"

        recommendations = """1. Przygotować plan testów
2. Wykonać testy funkcjonalne i niefunkcjonalne
3. Udokumentować wyniki testów
4. Zgłosić ewentualne błędy lub problemy
5. Potwierdzić akceptację funkcjonalności"""

    else:
        header = "📝 **UWAGA** - "
        explanation = f"Wymaga dodatkowej analizy:\n"
        explanation += f"- Brak jednoznacznych wskaźników typu (bug: {bug_score}, improvement: {improvement_score})\n"
        explanation += f"- Kontekst wymaga głębszej analizy\n"
        explanation += f"- Może wymagać konsultacji z zespołem"

        recommendations = """1. Przeanalizować szczegółowo kontekst
2. Skonsultować z zespołem lub klientem
3. Określić priorytet i typ działania
4. Zaplanować odpowiednie kroki
5. Monitorować postęp i wyniki"""

    return {
        'header': header,
        'explanation': explanation,
        'recommendations': recommendations,
        'type': issue_type,
        'scores': scores
    }

def process_clockify_file():
    """Przetwarza plik Clockify i dodaje kolumnę z odpowiedziami Jarka"""
    
    filename = "clockify_report.xlsx"
    
    try:
        # Wczytaj plik Excel
        print(f"Wczytywanie pliku: {filename}")
        
        # Sprawdź dostępne arkusze
        xl_file = pd.ExcelFile(filename)
        print(f"Dostępne arkusze: {xl_file.sheet_names}")
        
        # Wczytaj arkusz "Detailed Report"
        if "Detailed Report" in xl_file.sheet_names:
            df = pd.read_excel(filename, sheet_name="Detailed Report")
        else:
            print("Nie znaleziono arkusza 'Detailed Report'. Używam pierwszego arkusza.")
            df = pd.read_excel(filename, sheet_name=0)
        
        print(f"Wczytano {len(df)} wierszy")
        print(f"Kolumny: {list(df.columns)}")
        
        # Znajdź kolumnę Simon
        simon_column = None
        for col in df.columns:
            if 'simon' in str(col).lower():
                simon_column = col
                break
        
        if simon_column is None:
            print("Nie znaleziono kolumny 'Simon'")
            return
        
        print(f"Znaleziono kolumnę Simon: {simon_column}")
        
        # Utwórz kolumnę Jarek
        jarek_responses = []
        processed_jira_keys = set()  # Śledzenie przetworzonych kluczy JIRA

        for index, row in df.iterrows():
            simon_comment = row.get(simon_column, "")

            # Pobierz dodatkowe informacje z wiersza
            description_text = ""
            task_text = ""

            # Spróbuj znaleźć kolumny z opisem zadania
            for col in df.columns:
                col_lower = str(col).lower()
                if any(word in col_lower for word in ['description', 'task', 'project', 'opis', 'zadanie']):
                    if pd.notna(row.get(col)):
                        if 'description' in col_lower or 'opis' in col_lower:
                            description_text += str(row.get(col, "")) + " "
                        elif 'task' in col_lower or 'zadanie' in col_lower:
                            task_text += str(row.get(col, "")) + " "

            # Generuj odpowiedź
            if pd.notna(simon_comment) and str(simon_comment).strip():
                print(f"Przetwarzanie wiersza {index + 1}: {str(simon_comment)[:50]}...")
                response = analyze_simon_comment(
                    simon_comment,
                    description_text,
                    task_text,
                    row_data=row.to_dict(),
                    processed_jira_keys=processed_jira_keys
                )
            else:
                response = ""

            jarek_responses.append(response)
        
        # Dodaj kolumnę Jarek
        df['Jarek'] = jarek_responses
        
        # Zapisz do nowego pliku
        output_filename = f"Clockify_with_Jarek_responses_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        with pd.ExcelWriter(output_filename, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Detailed Report', index=False)
            
            # Dostosuj szerokość kolumn
            worksheet = writer.sheets['Detailed Report']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 100)  # Maksymalna szerokość 100
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        print(f"Plik został zapisany jako: {output_filename}")
        print(f"Dodano {len([r for r in jarek_responses if r])} odpowiedzi Jarka")
        
        return output_filename
        
    except Exception as e:
        print(f"Błąd podczas przetwarzania pliku: {e}")
        return None

def main():
    """Główna funkcja"""
    print("Analiza komentarzy Simona i generowanie odpowiedzi Jarka...")
    
    output_file = process_clockify_file()
    
    if output_file:
        print(f"\n✅ Sukces! Plik został przetworzony: {output_file}")
        print("\nKolumna 'Jarek' zawiera:")
        print("- Analizę typu komentarza (Bug/Improvement/Pytanie/etc.)")
        print("- Szczegóły z JIRA dla znalezionych numerów zadań")
        print("- Rekomendacje działań")
    else:
        print("\n❌ Wystąpił błąd podczas przetwarzania pliku")

if __name__ == "__main__":
    main()
