#!/usr/bin/env py
# -*- coding: utf-8 -*-

import pandas as pd
import re

def deep_translate_polish_to_english(text):
    """Deep translation of Polish text to English with comprehensive dictionary"""
    if pd.isna(text) or not isinstance(text, str):
        return text
    
    # Comprehensive Polish to English dictionary
    translations = {
        # Complete phrases first (longest first to avoid partial replacements)
        'Najprawdopodobniej różnice wynikały z różnego sposobu obliczania ile mineło od poprzedniego readingu': 'Most likely the differences resulted from different calculation methods for time elapsed since the previous reading',
        'przez co mogły się różnić wyniki': 'which could cause different results',
        'różnego sposobu obliczania ile mineło od poprz': 'different calculation method for time elapsed from previous',
        'różnego sposobu obliczania': 'different calculation method',
        'ile mineło od poprzedniego': 'time elapsed since previous',
        'od poprzedniego readingu': 'from the previous reading',
        'przez co mogły się różnić': 'which could differ',
        'wynikały z różnego sposobu': 'resulted from different method',
        'Najprawdopodobniej różnice': 'Most likely the differences',
        'Najprawdopodobniej': 'Most likely',
        'różnice wynikały': 'differences resulted',
        'sposobu obliczania': 'calculation method',
        'ile mineło': 'time elapsed',
        'od poprz': 'from previous',
        'poprzedniego': 'previous',
        'readingu': 'reading',
        'przez co': 'which',
        'mogły się': 'could',
        'różnić': 'differ',
        'wyniki': 'results',
        'wynikały': 'resulted',
        'różnego': 'different',
        'sposobu': 'method',
        'obliczania': 'calculating',
        'mineło': 'elapsed',
        'poprz': 'previous',
        'różnice': 'differences',
        
        # Technical terms
        'wyszukiwania': 'search',
        'parametrów': 'parameters',
        'parametry': 'parameters',
        'dodawanie': 'adding',
        'usuwanie': 'removing',
        'edycja': 'editing',
        'aktualizacja': 'update',
        'konfiguracja': 'configuration',
        'ustawienia': 'settings',
        'opcje': 'options',
        'funkcjonalność': 'functionality',
        'funkcje': 'functions',
        'interfejs': 'interface',
        'użytkownika': 'user',
        'system': 'system',
        'aplikacja': 'application',
        'moduł': 'module',
        'komponent': 'component',
        'usługa': 'service',
        'serwer': 'server',
        'baza danych': 'database',
        'dane': 'data',
        'informacje': 'information',
        'raport': 'report',
        'raporty': 'reports',
        'wyświetlanie': 'display',
        'pokazywanie': 'showing',
        'formularz': 'form',
        'formularze': 'forms',
        'walidacja': 'validation',
        'sprawdzanie': 'checking',
        'weryfikacja': 'verification',
        'testowanie': 'testing',
        'testy': 'tests',
        'błąd': 'error',
        'błędy': 'errors',
        'problem': 'issue',
        'problemy': 'issues',
        'poprawka': 'fix',
        'poprawki': 'fixes',
        'naprawa': 'repair',
        'ulepszenie': 'improvement',
        'ulepszenia': 'improvements',
        'usprawnienie': 'enhancement',
        'usprawnienia': 'enhancements',
        'optymalizacja': 'optimization',
        'wydajność': 'performance',
        'szybkość': 'speed',
        'czas': 'time',
        'data': 'date',
        'godzina': 'hour',
        'minuta': 'minute',
        'sekunda': 'second',
        'dzień': 'day',
        'tydzień': 'week',
        'miesiąc': 'month',
        'rok': 'year',
        'okres': 'period',
        'czas trwania': 'duration',
        'długość': 'length',
        'rozmiar': 'size',
        'wielkość': 'size',
        'ilość': 'quantity',
        'liczba': 'number',
        'wartość': 'value',
        'wartości': 'values',
        'suma': 'sum',
        'całość': 'total',
        'razem': 'total',
        'łącznie': 'total',
        'wszystko': 'all',
        'każdy': 'each',
        'wszystkie': 'all',
        'część': 'part',
        'części': 'parts',
        'element': 'element',
        'elementy': 'elements',
        'składnik': 'component',
        'składniki': 'components',
        'fragment': 'fragment',
        'fragmenty': 'fragments',
        'sekcja': 'section',
        'sekcje': 'sections',
        'kategoria': 'category',
        'kategorie': 'categories',
        'typ': 'type',
        'typy': 'types',
        'rodzaj': 'type',
        'rodzaje': 'types',
        'klasa': 'class',
        'klasy': 'classes',
        'grupa': 'group',
        'grupy': 'groups',
        'lista': 'list',
        'listy': 'lists',
        'tabela': 'table',
        'tabele': 'tables',
        'kolumna': 'column',
        'kolumny': 'columns',
        'wiersz': 'row',
        'wiersze': 'rows',
        'pole': 'field',
        'pola': 'fields',
        'przycisk': 'button',
        'przyciski': 'buttons',
        'menu': 'menu',
        'strona': 'page',
        'strony': 'pages',
        'ekran': 'screen',
        'ekrany': 'screens',
        'okno': 'window',
        'okna': 'windows',
        'dialog': 'dialog',
        'komunikat': 'message',
        'komunikaty': 'messages',
        'powiadomienie': 'notification',
        'powiadomienia': 'notifications',
        'alert': 'alert',
        'alerty': 'alerts',
        'ostrzeżenie': 'warning',
        'ostrzeżenia': 'warnings',
        'informacja': 'information',
        'szczegóły': 'details',
        'opis': 'description',
        'opisy': 'descriptions',
        'komentarz': 'comment',
        'komentarze': 'comments',
        'uwaga': 'note',
        'uwagi': 'notes',
        'notatka': 'note',
        'notatki': 'notes',
        'dokumentacja': 'documentation',
        'instrukcja': 'instruction',
        'instrukcje': 'instructions',
        'przewodnik': 'guide',
        'pomoc': 'help',
        'wsparcie': 'support',
        'obsługa': 'support',
        'serwis': 'service',
        'konserwacja': 'maintenance',
        'utrzymanie': 'maintenance',
        'zarządzanie': 'management',
        'administracja': 'administration',
        'konfigurowanie': 'configuring',
        'instalacja': 'installation',
        'instalowanie': 'installing',
        'wdrożenie': 'deployment',
        'wdrażanie': 'deploying',
        'publikacja': 'publication',
        'publikowanie': 'publishing',
        'wydanie': 'release',
        'wersja': 'version',
        'wersje': 'versions',
        'aktualizowanie': 'updating',
        'modernizacja': 'modernization',
        'rozwój': 'development',
        'programowanie': 'programming',
        'kodowanie': 'coding',
        'implementacja': 'implementation',
        'realizacja': 'implementation',
        'wykonanie': 'execution',
        'uruchomienie': 'launch',
        'start': 'start',
        'zatrzymanie': 'stop',
        'pauza': 'pause',
        'wznowienie': 'resume',
        'restart': 'restart',
        'reset': 'reset',
        'przywrócenie': 'restore',
        'cofnięcie': 'rollback',
        'anulowanie': 'cancellation',
        'odwołanie': 'cancellation',
        'potwierdzenie': 'confirmation',
        'akceptacja': 'acceptance',
        'zatwierdzenie': 'approval',
        'odrzucenie': 'rejection',
        'odmowa': 'denial',
        'zgoda': 'consent',
        'pozwolenie': 'permission',
        'zezwolenie': 'authorization',
        'uprawnienia': 'permissions',
        'dostęp': 'access',
        'bezpieczeństwo': 'security',
        'autoryzacja': 'authorization',
        'uwierzytelnianie': 'authentication',
        'logowanie': 'logging',
        'wylogowanie': 'logout',
        'sesja': 'session',
        'sesje': 'sessions',
        'token': 'token',
        'tokeny': 'tokens',
        'klucz': 'key',
        'klucze': 'keys',
        'hasło': 'password',
        'hasła': 'passwords',
        'użytkownik': 'user',
        'użytkownicy': 'users',
        'konto': 'account',
        'konta': 'accounts',
        'profil': 'profile',
        'profile': 'profiles',
        'rola': 'role',
        'role': 'roles',
        'grupa': 'group',
        'grupy': 'groups',
        'organizacja': 'organization',
        'organizacje': 'organizations',
        'firma': 'company',
        'firmy': 'companies',
        'projekt': 'project',
        'projekty': 'projects',
        'zadanie': 'task',
        'zadania': 'tasks',
        'cel': 'goal',
        'cele': 'goals',
        'wymaganie': 'requirement',
        'wymagania': 'requirements',
        'specyfikacja': 'specification',
        'definicja': 'definition',
        'charakterystyka': 'characteristics',
        'właściwości': 'properties',
        'atrybuty': 'attributes',
        'zmienne': 'variables',
        'stałe': 'constants',
        'preferencje': 'preferences',
        'wybory': 'choices',
        'alternatywy': 'alternatives',
        'możliwości': 'possibilities',
        'warianty': 'variants',
        'scenariusze': 'scenarios',
        'przypadki': 'cases',
        'sytuacje': 'situations',
        'warunki': 'conditions',
        'ograniczenia': 'limitations',
        'restrykcje': 'restrictions',
        'zasady': 'rules',
        'reguły': 'rules',
        'polityki': 'policies',
        'procedury': 'procedures',
        'procesy': 'processes',
        'przepływy': 'flows',
        'workflow': 'workflow',
        'cykl': 'cycle',
        'etap': 'stage',
        'etapy': 'stages',
        'faza': 'phase',
        'fazy': 'phases',
        'krok': 'step',
        'kroki': 'steps',
        'działanie': 'action',
        'działania': 'actions',
        'operacja': 'operation',
        'operacje': 'operations',
        'funkcjonowanie': 'functioning',
        'praca': 'work',
        'funkcja': 'function',
        'metoda': 'method',
        'sposób': 'method',
        'technika': 'technique',
        'podejście': 'approach',
        'strategia': 'strategy',
        'plan': 'plan',
        'plany': 'plans',
        'harmonogram': 'schedule',
        'terminy': 'deadlines',
        'deadline': 'deadline',
        
        # Status terms
        'nowy': 'new',
        'nowe': 'new',
        'otwarte': 'open',
        'otwarty': 'open',
        'zamknięte': 'closed',
        'zamknięty': 'closed',
        'w trakcie': 'in progress',
        'w realizacji': 'in progress',
        'zakończone': 'completed',
        'zakończony': 'completed',
        'ukończone': 'completed',
        'ukończony': 'completed',
        'anulowane': 'cancelled',
        'anulowany': 'cancelled',
        'odrzucone': 'rejected',
        'odrzucony': 'rejected',
        'zaakceptowane': 'accepted',
        'zaakceptowany': 'accepted',
        'zatwierdzone': 'approved',
        'zatwierdzony': 'approved',
        'oczekujące': 'pending',
        'oczekujący': 'pending',
        'wstrzymane': 'on hold',
        'wstrzymany': 'on hold',
        'aktywne': 'active',
        'aktywny': 'active',
        'nieaktywne': 'inactive',
        'nieaktywny': 'inactive',
        'dostępne': 'available',
        'dostępny': 'available',
        'niedostępne': 'unavailable',
        'niedostępny': 'unavailable',
        'włączone': 'enabled',
        'włączony': 'enabled',
        'wyłączone': 'disabled',
        'wyłączony': 'disabled',
        'publiczne': 'public',
        'publiczny': 'public',
        'prywatne': 'private',
        'prywatny': 'private',
        'ukryte': 'hidden',
        'ukryty': 'hidden',
        'widoczne': 'visible',
        'widoczny': 'visible',
        'obowiązkowe': 'required',
        'obowiązkowy': 'required',
        'opcjonalne': 'optional',
        'opcjonalny': 'optional',
        'automatyczne': 'automatic',
        'automatyczny': 'automatic',
        'ręczne': 'manual',
        'ręczny': 'manual',
        
        # Priority terms
        'niski': 'low',
        'niska': 'low',
        'niskie': 'low',
        'średni': 'medium',
        'średnia': 'medium',
        'średnie': 'medium',
        'wysoki': 'high',
        'wysoka': 'high',
        'wysokie': 'high',
        'krytyczny': 'critical',
        'krytyczna': 'critical',
        'krytyczne': 'critical',
        'pilny': 'urgent',
        'pilna': 'urgent',
        'pilne': 'urgent',
        'normalny': 'normal',
        'normalna': 'normal',
        'normalne': 'normal',
        
        # Common words
        'tak': 'yes',
        'nie': 'no',
        'może': 'maybe',
        'prawda': 'true',
        'fałsz': 'false',
        'prawdziwy': 'true',
        'fałszywy': 'false',
        'poprawny': 'correct',
        'poprawna': 'correct',
        'poprawne': 'correct',
        'niepoprawny': 'incorrect',
        'niepoprawna': 'incorrect',
        'niepoprawne': 'incorrect',
        'prawidłowy': 'valid',
        'prawidłowa': 'valid',
        'prawidłowe': 'valid',
        'nieprawidłowy': 'invalid',
        'nieprawidłowa': 'invalid',
        'nieprawidłowe': 'invalid',
        'możliwy': 'possible',
        'możliwa': 'possible',
        'możliwe': 'possible',
        'niemożliwy': 'impossible',
        'niemożliwa': 'impossible',
        'niemożliwe': 'impossible',
        'konieczny': 'necessary',
        'konieczna': 'necessary',
        'konieczne': 'necessary',
        'niepotrzebny': 'unnecessary',
        'niepotrzebna': 'unnecessary',
        'niepotrzebne': 'unnecessary',
        'ważny': 'important',
        'ważna': 'important',
        'ważne': 'important',
        'nieważny': 'unimportant',
        'nieważna': 'unimportant',
        'nieważne': 'unimportant',
        'główny': 'main',
        'główna': 'main',
        'główne': 'main',
        'dodatkowy': 'additional',
        'dodatkowa': 'additional',
        'dodatkowe': 'additional',
        'podstawowy': 'basic',
        'podstawowa': 'basic',
        'podstawowe': 'basic',
        'zaawansowany': 'advanced',
        'zaawansowana': 'advanced',
        'zaawansowane': 'advanced',
        'prosty': 'simple',
        'prosta': 'simple',
        'proste': 'simple',
        'złożony': 'complex',
        'złożona': 'complex',
        'złożone': 'complex',
        'skomplikowany': 'complicated',
        'skomplikowana': 'complicated',
        'skomplikowane': 'complicated',
        'łatwy': 'easy',
        'łatwa': 'easy',
        'łatwe': 'easy',
        'trudny': 'difficult',
        'trudna': 'difficult',
        'trudne': 'difficult',
        'szybki': 'fast',
        'szybka': 'fast',
        'szybkie': 'fast',
        'wolny': 'slow',
        'wolna': 'slow',
        'wolne': 'slow',
        'duży': 'large',
        'duża': 'large',
        'duże': 'large',
        'mały': 'small',
        'mała': 'small',
        'małe': 'small',
        'długi': 'long',
        'długa': 'long',
        'długie': 'long',
        'krótki': 'short',
        'krótka': 'short',
        'krótkie': 'short',
        'szeroki': 'wide',
        'szeroka': 'wide',
        'szerokie': 'wide',
        'wąski': 'narrow',
        'wąska': 'narrow',
        'wąskie': 'narrow',
        'głęboki': 'deep',
        'głęboka': 'deep',
        'głębokie': 'deep',
        'płytki': 'shallow',
        'płytka': 'shallow',
        'płytkie': 'shallow',
        'stary': 'old',
        'stara': 'old',
        'stare': 'old',
        'nowy': 'new',
        'nowa': 'new',
        'nowe': 'new',
        'młody': 'young',
        'młoda': 'young',
        'młode': 'young',
        'dobry': 'good',
        'dobra': 'good',
        'dobre': 'good',
        'zły': 'bad',
        'zła': 'bad',
        'złe': 'bad',
        'lepszy': 'better',
        'lepsza': 'better',
        'lepsze': 'better',
        'gorszy': 'worse',
        'gorsza': 'worse',
        'gorsze': 'worse',
        'najlepszy': 'best',
        'najlepsza': 'best',
        'najlepsze': 'best',
        'najgorszy': 'worst',
        'najgorsza': 'worst',
        'najgorsze': 'worst'
    }
    
    result = str(text)
    
    # Apply translations in order of length (longest first)
    for polish, english in sorted(translations.items(), key=lambda x: len(x[0]), reverse=True):
        # Use word boundaries to avoid partial replacements
        pattern = r'\b' + re.escape(polish) + r'\b'
        result = re.sub(pattern, english, result, flags=re.IGNORECASE)
    
    return result

def main():
    """Main function to deeply translate all Polish content"""
    
    filename = "Clockify_FINAL_Professional_Simon_Analysis.xlsx"
    
    print("Loading Excel file for deep translation...")
    
    try:
        # Load the Excel file
        df = pd.read_excel(filename, sheet_name='Complete Analysis')
        print(f"Loaded {len(df)} rows and {len(df.columns)} columns")
        
        # Apply deep translation to all text columns
        print("Applying deep translation to all content...")
        
        for col in df.columns:
            if df[col].dtype == 'object':  # Only process text columns
                print(f"Deep translating column: {col}")
                df[col] = df[col].apply(deep_translate_polish_to_english)
        
        # Save the deeply translated file
        df.to_excel(filename, sheet_name='Complete Analysis', index=False)
        print(f"Deep translation completed and saved to: {filename}")
        
        # Check for remaining Polish words
        print("\nChecking for remaining Polish content...")
        polish_indicators = [
            'ą', 'ć', 'ę', 'ł', 'ń', 'ó', 'ś', 'ź', 'ż',  # Polish characters
            'przez', 'które', 'mogły', 'się', 'różnić', 'wyniki',  # Common Polish words
            'sposób', 'obliczania', 'mineło', 'poprz', 'wynikały'
        ]
        
        remaining_polish = []
        for col in df.columns:
            if df[col].dtype == 'object':
                for indicator in polish_indicators:
                    if df[col].astype(str).str.contains(indicator, case=False, na=False).any():
                        # Find specific examples
                        examples = df[df[col].astype(str).str.contains(indicator, case=False, na=False)][col].head(2).tolist()
                        remaining_polish.append(f"{indicator} in {col}: {examples}")
        
        if remaining_polish:
            print("⚠️  Remaining Polish content found:")
            for item in remaining_polish[:10]:  # Show first 10
                print(f"  - {item}")
        else:
            print("✅ No Polish content detected - translation complete!")
        
        # Show examples of translated content
        print("\n=== EXAMPLES OF TRANSLATED CONTENT ===")
        
        # Find JIRA comment example
        for idx, row in df.iterrows():
            jira_info = str(row.get('JIRA Information', ''))
            if 'Most likely' in jira_info and 'RASEA-939' in jira_info:
                print(f"JIRA Comment Translation Example (Row {idx+1}):")
                print(jira_info[:400] + "...")
                break
        
        return filename
        
    except Exception as e:
        print(f"Error processing file: {e}")
        return None

if __name__ == "__main__":
    main()
