#!/usr/bin/env py
# -*- coding: utf-8 -*-

import pandas as pd
import requests
import base64
import re
import json
from datetime import datetime, timedelta
import openpyxl
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment

def translate_polish_fragments(text):
    """Translate ALL Polish fragments while preserving English structure"""
    if pd.isna(text) or not isinstance(text, str):
        return text
    
    # Comprehensive dictionary of Polish phrases to translate
    polish_translations = {
        # Common phrases from comments
        'Najprawdopodobniej': 'Most likely',
        'differences wynikały z': 'differences resulted from',
        'różnego sposobu': 'different method of',
        'obliczania': 'calculating',
        'ile mineło': 'how much time passed',
        'od poprzedniego': 'from the previous',
        'readingu': 'reading',
        'przez co': 'which caused',
        'wynikały z': 'resulted from',
        'sposobu': 'method',
        'mineło': 'passed',
        'poprzedniego': 'previous',
        'wynikały': 'resulted',
        'różnego': 'different',
        
        # Meeting and development phrases
        'po spotkaniu': 'after the meeting',
        'Aplikujemy taką samą': 'We apply the same',
        'logikę kalkulacji': 'calculation logic',
        'Przetestowane przez': 'Tested by',
        'Developera': 'Developer',
        'Nadal no mamy': 'We still have no',
        'żadnego śladu': 'trace',
        'Dodałam': 'I added',
        'które powinny': 'which should',
        'załatwić kwestię': 'resolve the issue',
        
        # Technical terms
        'editing statków': 'vessel editing',
        'no działa': 'does not work',
        'Utworzenie': 'Creating',
        'information ta': 'this information',
        'bedzie wysłana': 'will be sent',
        'na backend': 'to backend',
        'statków': 'vessels',
        'działa': 'works',
        'nie działa': 'does not work',
        
        # Common words and phrases
        'wydaje mi się': 'it seems to me',
        'że dla mnie': 'that for me',
        'by był tylko': 'there would be only',
        'Potrzebowalibyśmy': 'We would need',
        'testów': 'tests',
        'czy issue': 'whether the issue',
        'dotyczy': 'concerns',
        'nowych danych': 'new data',
        'Usprawnienie': 'Enhancement',
        'wyszukiwania': 'search',
        'Dodawanie': 'Adding',
        'parametrów': 'parameters',
        'do url': 'to URL',
        'różnica': 'difference',
        'między': 'between',
        'wynikami': 'results',
        'raportem': 'report',
        'wydajności': 'performance',
        
        # Technical terms
        'formularz': 'form',
        'walidacja': 'validation',
        'użytkownika': 'user',
        'roli': 'role',
        'ładunku': 'cargo',
        'miernika': 'meter',
        'formuły': 'formula',
        'paliwa': 'fuel',
        'uprawnienia': 'permissions',
        'zakładki': 'tabs',
        'raportów': 'reports',
        'ładowanie': 'loading',
        'modal': 'modal',
        'menu': 'menu',
        'strona': 'page',
        'notatka': 'note',
        
        # Additional common words
        'dodaj': 'add',
        'usuń': 'remove',
        'edytuj': 'edit',
        'zapisz': 'save',
        'anuluj': 'cancel',
        'potwierdź': 'confirm',
        'sprawdź': 'check',
        'przetestuj': 'test',
        'błąd': 'error',
        'problem': 'problem',
        'rozwiązanie': 'solution',
        'funkcja': 'function',
        'feature': 'feature',
        'bug': 'bug',
        'task': 'task',
        'zadanie': 'task'
    }
    
    # Apply translations
    result = text
    for polish, english in polish_translations.items():
        result = result.replace(polish, english)
        # Also try case-insensitive for better coverage
        if len(polish) > 3:
            result = re.sub(re.escape(polish), english, result, flags=re.IGNORECASE)
    
    return result

def get_jira_data():
    """Fetch fresh data from JIRA (both RASEA and RS projects)"""
    print("Fetching fresh JIRA data...")
    
    # JIRA configuration
    JIRA_URL = 'https://read-at-sea.atlassian.net'
    EMAIL = '<EMAIL>'
    API_TOKEN = 'ATATT3xFfGF0o1tVy4he6mvThd2U0mZU2NCqHbZoCB216R6aPWZZUnKecNaRaTIFIBkmb3sTHDvEPtjnJ__aW1EGolFwrIFPuo5qY9Z4SEW2TlMMt8d8v0sLWKkGGMkjLSDQQyP2wT4BImUn_XaNGozE1-KzMj1GFxhAtdkdmwtMErw0hkwPo4Q=D1D6CC05'
    
    # Authorization
    auth_string = f'{EMAIL}:{API_TOKEN}'
    auth_bytes = auth_string.encode('ascii')
    auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
    
    headers = {
        'Authorization': f'Basic {auth_b64}',
        'Accept': 'application/json'
    }
    
    jira_issues = {}
    
    # Fetch RASEA issues
    try:
        url = f'{JIRA_URL}/rest/api/3/search'
        params = {
            'jql': 'project=RASEA ORDER BY created DESC',
            'maxResults': 200,
            'fields': 'key,summary,status,assignee,reporter,created,updated,priority,issuetype,description,comment'
        }
        
        response = requests.get(url, headers=headers, params=params)
        if response.status_code == 200:
            data = response.json()
            for issue in data.get('issues', []):
                jira_issues[issue['key']] = issue
            print(f"Fetched {len(data.get('issues', []))} RASEA issues")
        else:
            print(f"Error fetching RASEA issues: {response.status_code}")
    except Exception as e:
        print(f"Error fetching RASEA: {e}")
    
    # Fetch RS issues
    try:
        params['jql'] = 'project=RS ORDER BY created DESC'
        response = requests.get(url, headers=headers, params=params)
        if response.status_code == 200:
            data = response.json()
            for issue in data.get('issues', []):
                jira_issues[issue['key']] = issue
            print(f"Fetched {len(data.get('issues', []))} RS issues")
        else:
            print(f"Error fetching RS issues: {response.status_code}")
    except Exception as e:
        print(f"Error fetching RS: {e}")
    
    print(f"Total JIRA issues fetched: {len(jira_issues)}")
    return jira_issues

def get_bitbucket_data():
    """Fetch fresh data from Bitbucket repositories"""
    print("Fetching fresh Bitbucket data...")
    
    # Bitbucket configuration
    USERNAME = 'mkasoft'
    APP_PASSWORD = 'ATBBspuHWSzDP9kUnNBRJWbLyGAP7E2DE5BA'
    
    repositories = ['trust-react', 'trust-netcore', 'trust-azure-functions']
    all_commits = []
    
    # Calculate date range (last 3 months)
    end_date = datetime.now()
    start_date = end_date - timedelta(days=90)
    
    for repo in repositories:
        try:
            url = f"https://api.bitbucket.org/2.0/repositories/a-softy/{repo}/commits/master"
            
            response = requests.get(url, auth=(USERNAME, APP_PASSWORD))
            if response.status_code == 200:
                data = response.json()
                repo_commits = []
                
                for commit in data.get('values', []):
                    commit_date = datetime.fromisoformat(commit['date'].replace('Z', '+00:00'))
                    if commit_date >= start_date.replace(tzinfo=commit_date.tzinfo):
                        repo_commits.append({
                            'repository': repo,
                            'hash': commit['hash'][:8],
                            'message': commit['message'],
                            'author': commit['author']['raw'],
                            'date': commit['date']
                        })
                
                all_commits.extend(repo_commits)
                print(f"Fetched {len(repo_commits)} commits from {repo}")
            else:
                print(f"Error fetching {repo}: {response.status_code}")
        except Exception as e:
            print(f"Error fetching {repo}: {e}")
    
    print(f"Total Bitbucket commits fetched: {len(all_commits)}")
    return all_commits
