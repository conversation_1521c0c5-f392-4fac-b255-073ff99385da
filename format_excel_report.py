#!/usr/bin/env py
# -*- coding: utf-8 -*-

import pandas as pd
import openpyxl
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.worksheet.dimensions import ColumnDimension
import re

def comprehensive_translate_to_english(text):
    """Comprehensive translation of Polish terms to English"""
    if pd.isna(text) or not isinstance(text, str):
        return text
    
    # Comprehensive translation dictionary
    translations = {
        # Basic terms
        'Projekt': 'Project',
        'Klient': 'Client', 
        'Opis': 'Description',
        'Zadanie': 'Task',
        'Użytkownik': 'User',
        'Grupa': 'Group',
        'Tagi': 'Tags',
        'Płatne': 'Billable',
        'Data rozpoczęcia': 'Start Date',
        'Godzina rozpocz<PERSON>cia': 'Start Time', 
        'Data zakończenia': 'End Date',
        'Godzina zakończ<PERSON>': 'End Time',
        '<PERSON><PERSON> trwania (h)': 'Duration (h)',
        '<PERSON><PERSON> trwania (dziesiętny)': 'Duration (decimal)',
        '<PERSON><PERSON><PERSON> płatna (USD)': 'Billable Rate (USD)',
        'Kwota płatna (USD)': 'Billable Amount (USD)',
        'Kategoria': 'Category',
        'Bug': 'Bug',
        'Zadanie': 'Task',
        'Usprawnienie': 'Improvement',
        'Pytanie': 'Question',
        'Test': 'Test',
        'Tak': 'Yes',
        'Nie': 'No',
        'ReadAtSea': 'ReadAtSea',
        'Trust': 'Trust',
        
        # JIRA and development terms
        'wyszukiwania': 'search functionality',
        'Usprawnienie wyszukiwania': 'Search Enhancement',
        'Dodawanie parametrów wyszukiwania do url': 'Adding search parameters to URL',
        'parametrów wyszukiwania': 'search parameters',
        'wyszukiwanie': 'search',
        'parametry': 'parameters',
        'filtrowanie': 'filtering',
        'sortowanie': 'sorting',
        'raport': 'report',
        'raporty': 'reports',
        'wyniki': 'results',
        'konsumpcja': 'consumption',
        'wydajność': 'performance',
        'różnica': 'difference',
        'różnice': 'differences',
        'obliczenia': 'calculations',
        'obliczanie': 'calculation',
        'dane': 'data',
        'wyświetlanie': 'display',
        'pokazywanie': 'showing',
        'formularz': 'form',
        'formularze': 'forms',
        'walidacja': 'validation',
        'sprawdzanie': 'validation',
        'błąd': 'error',
        'błędy': 'errors',
        'problem': 'issue',
        'problemy': 'issues',
        'poprawka': 'fix',
        'poprawki': 'fixes',
        'naprawa': 'repair',
        'aktualizacja': 'update',
        'aktualizacje': 'updates',
        'ulepszenie': 'enhancement',
        'ulepszenia': 'enhancements',
        'funkcjonalność': 'functionality',
        'funkcje': 'functions',
        'funkcja': 'function',
        'interfejs': 'interface',
        'użytkownika': 'user',
        'strona': 'page',
        'strony': 'pages',
        'ekran': 'screen',
        'ekrany': 'screens',
        'menu': 'menu',
        'przycisk': 'button',
        'przyciski': 'buttons',
        'pole': 'field',
        'pola': 'fields',
        'tabela': 'table',
        'tabele': 'tables',
        'lista': 'list',
        'listy': 'lists',
        'kolumna': 'column',
        'kolumny': 'columns',
        'wiersz': 'row',
        'wiersze': 'rows',
        'wartość': 'value',
        'wartości': 'values',
        'konfiguracja': 'configuration',
        'ustawienia': 'settings',
        'opcje': 'options',
        'wybór': 'selection',
        'wybieranie': 'selecting',
        'edycja': 'editing',
        'edytowanie': 'editing',
        'dodawanie': 'adding',
        'usuwanie': 'removing',
        'kasowanie': 'deleting',
        'zapisywanie': 'saving',
        'ładowanie': 'loading',
        'pobieranie': 'fetching',
        'wysyłanie': 'sending',
        'przesyłanie': 'transmitting',
        'logowanie': 'logging',
        'autoryzacja': 'authorization',
        'uwierzytelnianie': 'authentication',
        'uprawnienia': 'permissions',
        'dostęp': 'access',
        'bezpieczeństwo': 'security',
        'szyfrowanie': 'encryption',
        'baza danych': 'database',
        'bazy danych': 'databases',
        'serwer': 'server',
        'serwery': 'servers',
        'aplikacja': 'application',
        'aplikacje': 'applications',
        'system': 'system',
        'systemy': 'systems',
        'moduł': 'module',
        'moduły': 'modules',
        'komponent': 'component',
        'komponenty': 'components',
        'usługa': 'service',
        'usługi': 'services',
        
        # Status and priority terms
        'PRODUKCJA': 'PRODUCTION',
        'W TRAKCIE': 'IN PROGRESS',
        'ZAKOŃCZONE': 'COMPLETED',
        'NOWE': 'NEW',
        'OTWARTE': 'OPEN',
        'ZAMKNIĘTE': 'CLOSED',
        'WYSOKIE': 'HIGH',
        'ŚREDNIE': 'MEDIUM',
        'NISKIE': 'LOW',
        'KRYTYCZNE': 'CRITICAL',
        'PILNE': 'URGENT',
        
        # Time and date terms
        'stycznia': 'January',
        'lutego': 'February', 
        'marca': 'March',
        'kwietnia': 'April',
        'maja': 'May',
        'czerwca': 'June',
        'lipca': 'July',
        'sierpnia': 'August',
        'września': 'September',
        'października': 'October',
        'listopada': 'November',
        'grudnia': 'December',
        'poniedziałek': 'Monday',
        'wtorek': 'Tuesday',
        'środa': 'Wednesday',
        'czwartek': 'Thursday',
        'piątek': 'Friday',
        'sobota': 'Saturday',
        'niedziela': 'Sunday',
        
        # Common phrases
        'Najprawdopodobniej': 'Most likely',
        'różnego sposobu': 'different method',
        'obliczania': 'calculating',
        'ile mineło': 'how much time passed',
        'od poprz': 'from previous',
        'wynikały z': 'resulted from',
        'differences': 'differences',
        'sposobu': 'method',
        'mineło': 'passed',
        'poprz': 'previous',
        'wynikały': 'resulted',
        'różnego': 'different',
        
        # Technical terms
        'endpoint': 'endpoint',
        'żądanie': 'request',
        'żądania': 'requests',
        'odpowiedź': 'response',
        'odpowiedzi': 'responses',
        'komunikacja': 'communication',
        'integracja': 'integration',
        'synchronizacja': 'synchronization',
        'import': 'import',
        'eksport': 'export',
        'migracja': 'migration',
        'backup': 'backup',
        'kopia zapasowa': 'backup',
        'przywracanie': 'restore',
        'monitoring': 'monitoring',
        'debugowanie': 'debugging',
        'testowanie': 'testing',
        'testy': 'tests',
        'weryfikacja': 'verification',
        'sprawdzenie': 'check',
        'kontrola': 'control',
        'zarządzanie': 'management',
        'administracja': 'administration',
        'konfigurowanie': 'configuring',
        'instalacja': 'installation',
        'wdrożenie': 'deployment',
        'publikacja': 'publication',
        'wydanie': 'release',
        'wersja': 'version',
        'wersje': 'versions',
        'aktualizowanie': 'updating',
        'modernizacja': 'modernization',
        'optymalizacja': 'optimization',
        'szybkość': 'speed',
        'czas': 'time',
        'czas odpowiedzi': 'response time',
        'czas ładowania': 'loading time',
        'przepustowość': 'throughput',
        'skalowalność': 'scalability',
        'dostępność': 'availability',
        'niezawodność': 'reliability',
        'stabilność': 'stability',
        'jakość': 'quality',
        'standard': 'standard',
        'standardy': 'standards',
        'zgodność': 'compliance',
        'certyfikacja': 'certification',
        'dokumentacja': 'documentation',
        'instrukcja': 'instruction',
        'instrukcje': 'instructions',
        'przewodnik': 'guide',
        'pomoc': 'help',
        'wsparcie': 'support',
        'obsługa': 'support',
        'serwis': 'service',
        'konserwacja': 'maintenance',
        'utrzymanie': 'maintenance',
        'rozwój': 'development',
        'programowanie': 'programming',
        'kodowanie': 'coding',
        'implementacja': 'implementation',
        'realizacja': 'implementation',
        'wykonanie': 'execution',
        'uruchomienie': 'launch',
        'start': 'start',
        'zatrzymanie': 'stop',
        'pauza': 'pause',
        'wznowienie': 'resume',
        'restart': 'restart',
        'reset': 'reset',
        'przywrócenie': 'restore',
        'cofnięcie': 'rollback',
        'anulowanie': 'cancellation',
        'odwołanie': 'cancellation',
        'potwierdzenie': 'confirmation',
        'akceptacja': 'acceptance',
        'zatwierdzenie': 'approval',
        'odrzucenie': 'rejection',
        'odmowa': 'denial',
        'zgoda': 'consent',
        'pozwolenie': 'permission',
        'zezwolenie': 'authorization',
        'licencja': 'license',
        'umowa': 'contract',
        'kontrakt': 'contract'
    }
    
    result = str(text)
    # Sort by length (longest first) to avoid partial replacements
    for polish, english in sorted(translations.items(), key=lambda x: len(x[0]), reverse=True):
        result = result.replace(polish, english)
    
    return result

def apply_excel_formatting(filename):
    """Apply professional formatting to Excel file"""
    
    # Load workbook
    wb = openpyxl.load_workbook(filename)
    ws = wb.active
    
    # Define colors and styles
    header_fill = PatternFill(start_color="2F5597", end_color="2F5597", fill_type="solid")  # Dark blue
    header_font = Font(color="FFFFFF", bold=True, size=11)  # White, bold
    
    simon_fill = PatternFill(start_color="FFE6CC", end_color="FFE6CC", fill_type="solid")  # Light orange
    jira_fill = PatternFill(start_color="E6F3FF", end_color="E6F3FF", fill_type="solid")   # Light blue
    bitbucket_fill = PatternFill(start_color="E6FFE6", end_color="E6FFE6", fill_type="solid")  # Light green
    effort_fill = PatternFill(start_color="FFF2E6", end_color="FFF2E6", fill_type="solid")  # Light yellow
    analysis_fill = PatternFill(start_color="F0E6FF", end_color="F0E6FF", fill_type="solid")  # Light purple
    
    # Define borders
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # Define alignment
    center_alignment = Alignment(horizontal='center', vertical='center')
    wrap_alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
    
    # Apply header formatting
    for cell in ws[1]:
        cell.fill = header_fill
        cell.font = header_font
        cell.alignment = center_alignment
        cell.border = thin_border
    
    # Set row height for header
    ws.row_dimensions[1].height = 25
    
    # Apply formatting to data rows
    for row_num in range(2, ws.max_row + 1):
        for col_num in range(1, ws.max_column + 1):
            cell = ws.cell(row=row_num, column=col_num)
            cell.border = thin_border
            
            # Get column header to determine formatting
            header_cell = ws.cell(row=1, column=col_num)
            header_value = str(header_cell.value).lower()
            
            # Apply specific formatting based on column
            if 'simon' in header_value:
                cell.fill = simon_fill
                cell.alignment = wrap_alignment
            elif 'jira' in header_value:
                cell.fill = jira_fill
                cell.alignment = wrap_alignment
            elif 'bitbucket' in header_value:
                cell.fill = bitbucket_fill
                cell.alignment = wrap_alignment
            elif 'effort' in header_value or 'work' in header_value:
                cell.fill = effort_fill
                cell.alignment = center_alignment
            elif 'analysis' in header_value or 'comprehensive' in header_value:
                cell.fill = analysis_fill
                cell.alignment = wrap_alignment
            else:
                cell.alignment = center_alignment
        
        # Set row height for data rows
        ws.row_dimensions[row_num].height = 60
    
    # Set column widths
    column_widths = {
        'A': 15,  # Project
        'B': 15,  # Client
        'C': 30,  # Description
        'D': 20,  # Task
        'E': 15,  # User
        'F': 12,  # Group
        'G': 15,  # Tags
        'H': 10,  # Billable
        'I': 12,  # Start Date
        'J': 10,  # Start Time
        'K': 12,  # End Date
        'L': 10,  # End Time
        'M': 12,  # Duration (h)
        'N': 15,  # Duration (decimal)
        'O': 15,  # Billable Rate
        'P': 15,  # Billable Amount
        'Q': 15,  # Category
        'R': 25,  # Simon
        'S': 50,  # JIRA Information
        'T': 50,  # Bitbucket Development
        'U': 25,  # Work Effort Estimate
        'V': 50   # Comprehensive Analysis
    }
    
    for col_letter, width in column_widths.items():
        ws.column_dimensions[col_letter].width = width
    
    # Freeze panes (freeze first row and first 4 columns)
    ws.freeze_panes = 'E2'
    
    # Add autofilter
    ws.auto_filter.ref = f"A1:{chr(65 + ws.max_column - 1)}{ws.max_row}"
    
    # Save formatted workbook
    formatted_filename = filename.replace('.xlsx', '_FORMATTED.xlsx')
    wb.save(formatted_filename)
    
    return formatted_filename

def main():
    """Main function to translate and format Excel file"""
    
    source_filename = "Clockify_FINAL_Enhanced_Simon_Analysis.xlsx"
    
    print("Loading Excel file for translation and formatting...")
    
    try:
        # Load the Excel file
        df = pd.read_excel(source_filename, sheet_name='Complete Analysis')
        print(f"Loaded {len(df)} rows and {len(df.columns)} columns")
        
        # Translate all text content
        print("Translating all content to English...")
        
        # Translate column names (if not already translated)
        column_translations = {
            'Projekt': 'Project',
            'Klient': 'Client',
            'Opis': 'Description',
            'Zadanie': 'Task',
            'Użytkownik': 'User',
            'Grupa': 'Group',
            'Tagi': 'Tags',
            'Płatne': 'Billable',
            'Data rozpoczęcia': 'Start Date',
            'Godzina rozpoczęcia': 'Start Time',
            'Data zakończenia': 'End Date',
            'Godzina zakończenia': 'End Time',
            'Czas trwania (h)': 'Duration (h)',
            'Czas trwania (dziesiętny)': 'Duration (decimal)',
            'Stawka płatna (USD)': 'Billable Rate (USD)',
            'Kwota płatna (USD)': 'Billable Amount (USD)',
            'Kategoria': 'Category'
        }
        
        df = df.rename(columns=column_translations)
        
        # Translate all cell content
        for col in df.columns:
            if df[col].dtype == 'object':  # Only process text columns
                print(f"Translating column: {col}")
                df[col] = df[col].apply(comprehensive_translate_to_english)
        
        # Save translated version
        translated_filename = "Clockify_Translated_Simon_Analysis.xlsx"
        df.to_excel(translated_filename, sheet_name='Complete Analysis', index=False)
        print(f"Translated file saved as: {translated_filename}")
        
        # Apply formatting
        print("Applying professional formatting...")
        formatted_filename = apply_excel_formatting(translated_filename)
        print(f"Formatted file saved as: {formatted_filename}")
        
        # Create final version with better name
        final_filename = "Clockify_FINAL_Professional_Simon_Analysis.xlsx"
        
        # Copy formatted file to final name
        import shutil
        shutil.copy2(formatted_filename, final_filename)
        print(f"Final professional file: {final_filename}")
        
        # Print summary
        print(f"\n✅ SUCCESS!")
        print(f"📊 Summary:")
        print(f"- Original file: {source_filename}")
        print(f"- Translated file: {translated_filename}")
        print(f"- Formatted file: {formatted_filename}")
        print(f"- Final file: {final_filename}")
        print(f"- Rows processed: {len(df)}")
        print(f"- Columns processed: {len(df.columns)}")
        
        # Show column list
        print(f"\n📋 Final columns:")
        for i, col in enumerate(df.columns, 1):
            print(f"  {i:2d}. {col}")
        
        return final_filename
        
    except Exception as e:
        print(f"Error processing file: {e}")
        return None

if __name__ == "__main__":
    main()
