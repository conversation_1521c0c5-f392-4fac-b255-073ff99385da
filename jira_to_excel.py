#!/usr/bin/env py
# -*- coding: utf-8 -*-

import requests
import json
import pandas as pd
from datetime import datetime
import base64

# Konfiguracja JIRA
JIRA_URL = "https://read-at-sea.atlassian.net"
EMAIL = "j.w<PERSON><PERSON><PERSON>@a-soft.pl"
API_TOKEN = "ATATT3xFfGF0o1tVy4he6mvThd2U0mZU2NCqHbZoCB216R6aPWZZUnKecNaRaTIFIBkmb3sTHDvEPtjnJ__aW1EGolFwrIFPuo5qY9Z4SEW2TlMMt8d8v0sLWKkGGMkjLSDQQyP2wT4BImUn_XaNGozE1-KzMj1GFxhAtdkdmwtMErw0hkwPo4Q=D1D6CC05"

def get_jira_issues():
    """Pobiera zadania z JIRA"""
    
    # Przygotowanie autoryzacji
    auth_string = f"{EMAIL}:{API_TOKEN}"
    auth_bytes = auth_string.encode('ascii')
    auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
    
    headers = {
        'Authorization': f'Basic {auth_b64}',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }
    
    # JQL query - pobierz wszystkie zadania z projektu RS
    jql = "project = RS ORDER BY created DESC"
    
    url = f"{JIRA_URL}/rest/api/3/search"
    params = {
        'jql': jql,
        'maxResults': 100,
        'fields': 'key,summary,status,assignee,reporter,created,updated,priority,issuetype,description,components'
    }
    
    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Błąd podczas pobierania danych z JIRA: {e}")
        if hasattr(e.response, 'text'):
            print(f"Odpowiedź serwera: {e.response.text}")
        return None

def process_issues_to_excel(issues_data):
    """Przetwarza dane z JIRA i tworzy plik Excel"""
    
    if not issues_data or 'issues' not in issues_data:
        print("Brak danych do przetworzenia")
        return
    
    issues = issues_data['issues']
    
    # Lista do przechowywania danych
    data = []
    
    for issue in issues:
        fields = issue['fields']
        
        # Wyciągnij podstawowe informacje
        issue_data = {
            'Klucz': issue['key'],
            'Tytuł': fields.get('summary', ''),
            'Status': fields.get('status', {}).get('name', '') if fields.get('status') else '',
            'Typ': fields.get('issuetype', {}).get('name', '') if fields.get('issuetype') else '',
            'Priorytet': fields.get('priority', {}).get('name', '') if fields.get('priority') else '',
            'Przypisany do': fields.get('assignee', {}).get('displayName', '') if fields.get('assignee') else 'Nieprzypisane',
            'Reporter': fields.get('reporter', {}).get('displayName', '') if fields.get('reporter') else '',
            'Data utworzenia': fields.get('created', ''),
            'Data aktualizacji': fields.get('updated', ''),
            'Opis': fields.get('description', ''),
            'Komponenty': ', '.join([comp['name'] for comp in fields.get('components', [])]) if fields.get('components') else ''
        }
        
        # Formatuj daty
        for date_field in ['Data utworzenia', 'Data aktualizacji']:
            if issue_data[date_field]:
                try:
                    # Parsuj datę ISO 8601
                    dt = datetime.fromisoformat(issue_data[date_field].replace('Z', '+00:00'))
                    issue_data[date_field] = dt.strftime('%Y-%m-%d %H:%M:%S')
                except:
                    pass
        
        data.append(issue_data)
    
    # Utwórz DataFrame
    df = pd.DataFrame(data)
    
    # Zapisz do pliku Excel
    filename = f"jira_zadania_RS_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='Zadania JIRA', index=False)
        
        # Dostosuj szerokość kolumn
        worksheet = writer.sheets['Zadania JIRA']
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)  # Maksymalna szerokość 50
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    print(f"Plik Excel został utworzony: {filename}")
    print(f"Liczba zadań: {len(data)}")
    
    return filename

def main():
    """Główna funkcja"""
    print("Pobieranie zadań z JIRA...")
    
    # Pobierz dane z JIRA
    issues_data = get_jira_issues()
    
    if issues_data:
        print(f"Pobrano {issues_data.get('total', 0)} zadań")
        
        # Przetwórz i zapisz do Excel
        filename = process_issues_to_excel(issues_data)
        
        if filename:
            print(f"Zadania zostały zapisane do pliku: {filename}")
    else:
        print("Nie udało się pobrać danych z JIRA")

if __name__ == "__main__":
    main()
