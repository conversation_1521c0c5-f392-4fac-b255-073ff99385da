#!/usr/bin/env py
# -*- coding: utf-8 -*-

import pandas as pd
import openpyxl
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment

def translate_polish_fragments(text):
    """Translate ALL Polish fragments while preserving English structure"""
    if pd.isna(text) or not isinstance(text, str):
        return text

    # Comprehensive dictionary of Polish phrases to translate
    polish_translations = {
        # Common phrases from comments
        'Najprawdopodobniej': 'Most likely',
        'differences wynikały z': 'differences resulted from',
        'różnego sposobu': 'different method of',
        'obliczania': 'calculating',
        'ile mineło': 'how much time passed',
        'od poprzedniego': 'from the previous',
        'readingu': 'reading',
        'przez co': 'which caused',
        'wynikały z': 'resulted from',
        'sposobu': 'method',
        'mineło': 'passed',
        'poprzedniego': 'previous',
        'wynikały': 'resulted',
        'różnego': 'different',

        # Meeting and development phrases
        'po spotkaniu': 'after the meeting',
        'Aplikujemy taką samą': 'We apply the same',
        'logikę kalkulacji': 'calculation logic',
        'Przetestowane przez': 'Tested by',
        'Developera': 'Developer',
        'Nadal no mamy': 'We still have no',
        'żadnego śladu': 'trace',
        'Dodałam': 'I added',
        'które powinny': 'which should',
        'załatwić kwestię': 'resolve the issue',

        # Technical terms
        'editing statków': 'vessel editing',
        'no działa': 'does not work',
        'Utworzenie': 'Creating',
        'information ta': 'this information',
        'bedzie wysłana': 'will be sent',
        'na backend': 'to backend',
        'statków': 'vessels',
        'działa': 'works',
        'nie działa': 'does not work',

        # Common words
        'wydaje mi się': 'it seems to me',
        'że dla mnie': 'that for me',
        'by był tylko': 'there would be only',
        'slide': 'slide',
        'Potrzebowalibyśmy': 'We would need',
        'testów': 'tests',
        'czy issue': 'whether the issue',
        'dotyczy': 'concerns',
        'nowych danych': 'new data',

        # Additional common phrases
        'Usprawnienie': 'Enhancement',
        'wyszukiwania': 'search',
        'Dodawanie': 'Adding',
        'parametrów': 'parameters',
        'do url': 'to URL',
        'różnica': 'difference',
        'między': 'between',
        'wynikami': 'results',
        'raportem': 'report',
        'wydajności': 'performance',

        # More technical terms
        'formularz': 'form',
        'walidacja': 'validation',
        'użytkownika': 'user',
        'roli': 'role',
        'ładunku': 'cargo',
        'miernika': 'meter',
        'formuły': 'formula',
        'paliwa': 'fuel',
        'uprawnienia': 'permissions',
        'zakładki': 'tabs',
        'raportów': 'reports',
        'ładowanie': 'loading',
        'modal': 'modal',
        'menu': 'menu',
        'strona': 'page',
        'notatka': 'note',

        # Status and workflow
        'w trakcie': 'in progress',
        'zakończone': 'completed',
        'do zrobienia': 'to do',
        'priorytet': 'priority',
        'wysoki': 'high',
        'średni': 'medium',
        'niski': 'low',

        # Time and dates
        'dzisiaj': 'today',
        'wczoraj': 'yesterday',
        'jutro': 'tomorrow',
        'tydzień': 'week',
        'miesiąc': 'month',
        'rok': 'year',

        # Actions
        'dodaj': 'add',
        'usuń': 'remove',
        'edytuj': 'edit',
        'zapisz': 'save',
        'anuluj': 'cancel',
        'potwierdź': 'confirm',
        'sprawdź': 'check',
        'przetestuj': 'test'
    }

    # Apply translations (case-insensitive for better matching)
    result = text
    for polish, english in polish_translations.items():
        # Replace exact matches (case-sensitive first)
        result = result.replace(polish, english)
        # Then try case-insensitive for common words
        if len(polish) > 3:  # Only for longer words to avoid false positives
            import re
            pattern = re.compile(re.escape(polish), re.IGNORECASE)
            result = pattern.sub(english, result)

    return result

def create_properly_formatted_report():
    """Create properly formatted report from original file with gentle translation"""

    # Use the original enhanced file that has good content
    source_file = "Clockify_FINAL_Enhanced_Simon_Analysis.xlsx"
    output_file = "Clockify_PROPERLY_FORMATTED_Simon_Analysis.xlsx"

    print(f"Creating properly formatted report from {source_file}...")

    # Load the original data
    df = pd.read_excel(source_file, sheet_name='Complete Analysis')

    print(f"Loaded {len(df)} rows and {len(df.columns)} columns")

    # Apply gentle translation to analysis columns only
    analysis_columns = ['Simon', 'JIRA Information', 'Bitbucket Development', 'Comprehensive Analysis']

    for col in analysis_columns:
        if col in df.columns:
            print(f"Applying gentle translation to {col} column...")
            df[col] = df[col].apply(translate_polish_fragments)

    print("Gentle translation completed")
    
    # Save to new file first
    df.to_excel(output_file, sheet_name='Complete Analysis', index=False)
    
    # Now apply proper formatting
    wb = openpyxl.load_workbook(output_file)
    ws = wb.active
    
    # Define colors and styles
    header_fill = PatternFill(start_color="2F5597", end_color="2F5597", fill_type="solid")  # Dark blue
    header_font = Font(color="FFFFFF", bold=True, size=11)  # White, bold
    
    simon_fill = PatternFill(start_color="FFE6CC", end_color="FFE6CC", fill_type="solid")  # Light orange
    jira_fill = PatternFill(start_color="E6F3FF", end_color="E6F3FF", fill_type="solid")   # Light blue
    bitbucket_fill = PatternFill(start_color="E6FFE6", end_color="E6FFE6", fill_type="solid")  # Light green
    effort_fill = PatternFill(start_color="FFF2E6", end_color="FFF2E6", fill_type="solid")  # Light yellow
    analysis_fill = PatternFill(start_color="F0E6FF", end_color="F0E6FF", fill_type="solid")  # Light purple
    
    # Define borders
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # Define alignment
    center_alignment = Alignment(horizontal='center', vertical='center')
    wrap_alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
    
    # Define fonts
    normal_font = Font(size=10)  # Normal font for regular columns
    analysis_font = Font(size=9)  # Smaller font for analysis columns
    
    # Apply header formatting
    for cell in ws[1]:
        cell.fill = header_fill
        cell.font = header_font
        cell.alignment = center_alignment
        cell.border = thin_border
    
    # Set row height for header
    ws.row_dimensions[1].height = 25
    
    # Get column headers to identify analysis columns
    analysis_columns = []
    for col_num in range(1, ws.max_column + 1):
        header_cell = ws.cell(row=1, column=col_num)
        header_value = str(header_cell.value).lower()
        
        # Identify analysis columns
        if any(keyword in header_value for keyword in ['simon', 'jira', 'bitbucket', 'analysis', 'comprehensive', 'effort', 'work']):
            analysis_columns.append(col_num)
    
    print(f"Analysis columns identified: {len(analysis_columns)} columns")
    
    # Apply formatting to data rows
    for row_num in range(2, ws.max_row + 1):
        for col_num in range(1, ws.max_column + 1):
            cell = ws.cell(row=row_num, column=col_num)
            cell.border = thin_border
            
            # Get column header to determine formatting
            header_cell = ws.cell(row=1, column=col_num)
            header_value = str(header_cell.value).lower()
            
            # Apply specific formatting based on column type
            if 'simon' in header_value:
                cell.fill = simon_fill
                cell.alignment = wrap_alignment
                cell.font = analysis_font  # Font size 9 for Simon column
            elif 'jira' in header_value:
                cell.fill = jira_fill
                cell.alignment = wrap_alignment
                cell.font = analysis_font  # Font size 9 for JIRA column
            elif 'bitbucket' in header_value:
                cell.fill = bitbucket_fill
                cell.alignment = wrap_alignment
                cell.font = analysis_font  # Font size 9 for Bitbucket column
            elif 'effort' in header_value or 'work' in header_value:
                cell.fill = effort_fill
                cell.alignment = wrap_alignment  # Text wrapping for effort column
                cell.font = analysis_font  # Font size 9 for Work Effort column
            elif 'analysis' in header_value or 'comprehensive' in header_value:
                cell.fill = analysis_fill
                cell.alignment = wrap_alignment
                cell.font = analysis_font  # Font size 9 for Analysis column
            else:
                # Regular columns
                cell.alignment = center_alignment
                cell.font = normal_font
        
        # Set row height for data rows (increased for better readability with wrapped text)
        ws.row_dimensions[row_num].height = 80  # Increased for better readability
    
    # Set column widths (optimized for analysis columns)
    column_widths = {
        'A': 15,  # Project
        'B': 15,  # Client
        'C': 35,  # Description (increased)
        'D': 20,  # Task
        'E': 15,  # User
        'F': 12,  # Group
        'G': 15,  # Email
        'H': 15,  # Tags
        'I': 10,  # Billable
        'J': 12,  # Start Date
        'K': 10,  # Start Time
        'L': 12,  # End Date
        'M': 10,  # End Time
        'N': 12,  # Duration (h)
        'O': 15,  # Duration (decimal)
        'P': 15,  # Billable Rate
        'Q': 15,  # Billable Amount
        'R': 15,  # Category
        'S': 30,  # Simon (increased for better readability)
        'T': 60,  # JIRA Information (increased)
        'U': 60,  # Bitbucket Development (increased)
        'V': 30,  # Work Effort Estimate (increased)
        'W': 60   # Comprehensive Analysis (increased)
    }
    
    for col_letter, width in column_widths.items():
        if col_letter <= chr(65 + ws.max_column - 1):
            ws.column_dimensions[col_letter].width = width
    
    # Freeze panes (freeze first row and first 4 columns)
    ws.freeze_panes = 'E2'
    
    # Add autofilter
    ws.auto_filter.ref = f"A1:{chr(65 + ws.max_column - 1)}{ws.max_row}"
    
    # Save the formatted workbook
    wb.save(output_file)
    
    print(f"✅ Properly formatted report created: {output_file}")
    print("📊 Formatting applied:")
    print("  - Analysis columns use font size 9")
    print("  - All analysis columns have text wrapping enabled")
    print("  - Row height set to 80px for better readability")
    print("  - Column widths optimized for analysis content")
    print("  - Professional color scheme applied")
    print("  - Frozen panes and autofilter enabled")
    
    # Print summary of analysis columns
    analysis_column_names = []
    for col_num in analysis_columns:
        header_cell = ws.cell(row=1, column=col_num)
        analysis_column_names.append(str(header_cell.value))
    
    print(f"  - Analysis columns with font 9 and text wrap: {', '.join(analysis_column_names)}")
    
    return output_file

def main():
    """Main function"""
    try:
        result = create_properly_formatted_report()
        print(f"\n🎉 SUCCESS! Properly formatted report created: {result}")
        return result
    except Exception as e:
        print(f"❌ Error creating formatted report: {e}")
        return None

if __name__ == "__main__":
    main()
