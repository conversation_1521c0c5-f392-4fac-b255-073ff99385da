#!/usr/bin/env py
# -*- coding: utf-8 -*-

import openpyxl
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment

def format_excel():
    # Load workbook
    wb = openpyxl.load_workbook('Clockify_FINAL_Professional_Simon_Analysis.xlsx')
    ws = wb.active
    
    # Define colors
    header_fill = PatternFill(start_color="2F5597", end_color="2F5597", fill_type="solid")
    header_font = Font(color="FFFFFF", bold=True, size=11)
    
    simon_fill = PatternFill(start_color="FFE6CC", end_color="FFE6CC", fill_type="solid")
    jira_fill = PatternFill(start_color="E6F3FF", end_color="E6F3FF", fill_type="solid")
    bitbucket_fill = PatternFill(start_color="E6FFE6", end_color="E6FFE6", fill_type="solid")
    effort_fill = PatternFill(start_color="FFF2E6", end_color="FFF2E6", fill_type="solid")
    analysis_fill = PatternFill(start_color="F0E6FF", end_color="F0E6FF", fill_type="solid")
    
    # Define borders
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # Define alignment
    center_alignment = Alignment(horizontal='center', vertical='center')
    wrap_alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
    
    # Format header
    for cell in ws[1]:
        cell.fill = header_fill
        cell.font = header_font
        cell.alignment = center_alignment
        cell.border = thin_border
    
    ws.row_dimensions[1].height = 25
    
    # Format data rows
    for row_num in range(2, ws.max_row + 1):
        for col_num in range(1, ws.max_column + 1):
            cell = ws.cell(row=row_num, column=col_num)
            cell.border = thin_border
            
            header_cell = ws.cell(row=1, column=col_num)
            header_value = str(header_cell.value).lower()
            
            if 'simon' in header_value:
                cell.fill = simon_fill
                cell.alignment = wrap_alignment
            elif 'jira' in header_value:
                cell.fill = jira_fill
                cell.alignment = wrap_alignment
            elif 'bitbucket' in header_value:
                cell.fill = bitbucket_fill
                cell.alignment = wrap_alignment
            elif 'effort' in header_value or 'work' in header_value:
                cell.fill = effort_fill
                cell.alignment = center_alignment
            elif 'analysis' in header_value or 'comprehensive' in header_value:
                cell.fill = analysis_fill
                cell.alignment = wrap_alignment
            else:
                cell.alignment = center_alignment
        
        ws.row_dimensions[row_num].height = 60
    
    # Set column widths
    widths = [15, 15, 30, 20, 15, 12, 15, 10, 12, 10, 12, 10, 12, 15, 15, 15, 15, 25, 50, 50, 25, 50, 50]
    
    for i, width in enumerate(widths):
        if i < ws.max_column:
            col_letter = chr(65 + i)
            ws.column_dimensions[col_letter].width = width
    
    # Freeze panes
    ws.freeze_panes = 'E2'
    
    # Add autofilter
    ws.auto_filter.ref = f"A1:{chr(65 + ws.max_column - 1)}{ws.max_row}"
    
    # Save
    wb.save('Clockify_FINAL_Professional_Simon_Analysis.xlsx')
    
    print("Excel formatting completed!")

if __name__ == "__main__":
    format_excel()
