#!/usr/bin/env py
# -*- coding: utf-8 -*-

import pandas as pd
import requests
import json
import base64
import re
from datetime import datetime, timedelta
from collections import defaultdict

# Bitbucket Configuration
BITBUCKET_USERNAME = "mkasoft"
BITBUCKET_APP_PASSWORD = "ATBBspuHWSzDP9kUnNBRJWbLyGAP7E2DE5BA"
WORKSPACE = "a-softy"

# Repositories to analyze
REPOSITORIES = [
    "trust-netcore",
    "trust-react", 
    "trust-azure-functions"
]

def get_bitbucket_commits(repo_name, since_date=None):
    """Retrieves commits from Bitbucket repository"""
    
    auth_string = f"{BITBUCKET_USERNAME}:{BITBUCKET_APP_PASSWORD}"
    auth_bytes = auth_string.encode('ascii')
    auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
    
    headers = {
        'Authorization': f'Basic {auth_b64}',
        'Accept': 'application/json'
    }
    
    all_commits = []
    url = f"https://api.bitbucket.org/2.0/repositories/{WORKSPACE}/{repo_name}/commits/master"
    
    while url:
        try:
            print(f"Fetching commits from {repo_name}...")
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            data = response.json()
            
            commits = data.get('values', [])
            
            for commit in commits:
                commit_date = datetime.fromisoformat(commit['date'].replace('Z', '+00:00'))
                
                # Filter by date if specified
                if since_date and commit_date < since_date:
                    return all_commits  # Stop when we reach older commits
                
                # Add repository info to commit
                commit['repository'] = repo_name
                all_commits.append(commit)
            
            # Get next page
            url = data.get('next')
            
        except requests.exceptions.RequestException as e:
            print(f"Error fetching commits from {repo_name}: {e}")
            break
    
    return all_commits

def extract_jira_keys_from_commit(commit_message):
    """Extracts JIRA keys from commit message"""
    patterns = [
        r'\b(RASEA-\d+)\b',
        r'\b(RS-\d+)\b',
        r'\b([A-Z]+-\d+)\b'
    ]
    
    jira_keys = []
    for pattern in patterns:
        matches = re.findall(pattern, commit_message, re.IGNORECASE)
        jira_keys.extend(matches)
    
    return list(set(jira_keys))

def analyze_commit_activity(commits):
    """Analyzes commit activity and generates business insights"""
    
    # Group commits by repository
    repo_stats = defaultdict(lambda: {
        'commit_count': 0,
        'authors': set(),
        'jira_tasks': set(),
        'recent_activity': [],
        'file_changes': defaultdict(int)
    })
    
    # Group commits by author
    author_stats = defaultdict(lambda: {
        'commit_count': 0,
        'repositories': set(),
        'jira_tasks': set()
    })
    
    # Group commits by JIRA task
    jira_stats = defaultdict(lambda: {
        'commits': [],
        'repositories': set(),
        'authors': set()
    })
    
    for commit in commits:
        repo = commit['repository']
        # Handle different author field formats
        if 'author' in commit and commit['author']:
            if isinstance(commit['author'], dict):
                author = commit['author'].get('display_name', commit['author'].get('raw', 'Unknown'))
            else:
                author = str(commit['author'])
        else:
            author = 'Unknown'

        message = commit.get('message', '')
        date = commit.get('date', '')
        
        # Repository stats
        repo_stats[repo]['commit_count'] += 1
        repo_stats[repo]['authors'].add(author)
        repo_stats[repo]['recent_activity'].append({
            'date': date,
            'author': author,
            'message': message[:100] + '...' if len(message) > 100 else message
        })
        
        # Author stats
        author_stats[author]['commit_count'] += 1
        author_stats[author]['repositories'].add(repo)
        
        # Extract JIRA keys
        jira_keys = extract_jira_keys_from_commit(message)
        for jira_key in jira_keys:
            repo_stats[repo]['jira_tasks'].add(jira_key)
            author_stats[author]['jira_tasks'].add(jira_key)
            jira_stats[jira_key]['commits'].append({
                'repo': repo,
                'author': author,
                'message': message,
                'date': date
            })
            jira_stats[jira_key]['repositories'].add(repo)
            jira_stats[jira_key]['authors'].add(author)
    
    return repo_stats, author_stats, jira_stats

def generate_business_summary(repo_stats, author_stats, jira_stats):
    """Generates business-friendly summary of development activity"""
    
    summary = []
    
    # Overall project summary
    total_commits = sum(stats['commit_count'] for stats in repo_stats.values())
    total_authors = len(set().union(*[stats['authors'] for stats in repo_stats.values()]))
    total_jira_tasks = len(jira_stats)
    
    summary.append(f"**TRUST PROJECT DEVELOPMENT SUMMARY (Last 3 Months)**")
    summary.append(f"• Total commits: {total_commits}")
    summary.append(f"• Active developers: {total_authors}")
    summary.append(f"• JIRA tasks addressed: {total_jira_tasks}")
    summary.append(f"• Repositories updated: {len(repo_stats)}")
    summary.append("")
    
    # Repository-specific insights
    for repo, stats in repo_stats.items():
        summary.append(f"**{repo.upper()} REPOSITORY:**")
        summary.append(f"• Commits: {stats['commit_count']}")
        summary.append(f"• Contributors: {len(stats['authors'])}")
        summary.append(f"• JIRA tasks: {len(stats['jira_tasks'])}")
        
        # Recent activity highlights
        if stats['recent_activity']:
            summary.append(f"• Recent activity:")
            for activity in stats['recent_activity'][:3]:  # Show last 3 activities
                date_str = activity['date'][:10]  # Just the date part
                summary.append(f"  - {date_str}: {activity['message']} ({activity['author']})")
        
        summary.append("")
    
    # JIRA task insights
    if jira_stats:
        summary.append(f"**JIRA INTEGRATION INSIGHTS:**")
        for jira_key, stats in list(jira_stats.items())[:10]:  # Show top 10 JIRA tasks
            summary.append(f"• {jira_key}: {len(stats['commits'])} commits across {len(stats['repositories'])} repositories")
        summary.append("")
    
    # Developer productivity insights
    summary.append(f"**DEVELOPER ACTIVITY:**")
    sorted_authors = sorted(author_stats.items(), key=lambda x: x[1]['commit_count'], reverse=True)
    for author, stats in sorted_authors:
        summary.append(f"• {author}: {stats['commit_count']} commits, {len(stats['repositories'])} repositories, {len(stats['jira_tasks'])} JIRA tasks")
    
    return "\n".join(summary)

def find_related_commits_for_jira_task(jira_key, all_commits):
    """Finds commits related to a specific JIRA task"""
    
    related_commits = []
    
    for commit in all_commits:
        message = commit['message']
        jira_keys = extract_jira_keys_from_commit(message)
        
        if jira_key in jira_keys:
            # Handle author field safely
            if 'author' in commit and commit['author']:
                if isinstance(commit['author'], dict):
                    author = commit['author'].get('display_name', commit['author'].get('raw', 'Unknown'))
                else:
                    author = str(commit['author'])
            else:
                author = 'Unknown'

            related_commits.append({
                'repository': commit['repository'],
                'author': author,
                'date': commit['date'][:10],
                'message': message,
                'hash': commit.get('hash', commit.get('node', 'unknown'))[:8]
            })
    
    return related_commits

def enhance_excel_with_bitbucket_data(excel_file, all_commits):
    """Enhances Excel file with Bitbucket commit information"""
    
    try:
        # Load Excel file
        df = pd.read_excel(excel_file, sheet_name='Detailed Report')
        print(f"Loaded {len(df)} rows from Excel")
        
        # Create Bitbucket column
        bitbucket_responses = []
        
        for index, row in df.iterrows():
            # Get all text from row to find JIRA numbers
            all_row_text = ""
            for col, value in row.items():
                if pd.notna(value) and isinstance(value, str):
                    all_row_text += f" {value}"
            
            # Extract JIRA keys from row
            jira_keys = extract_jira_keys_from_commit(all_row_text)
            
            if jira_keys:
                print(f"Processing row {index + 1}: Found JIRA keys {jira_keys}")
                
                bitbucket_info = "**BITBUCKET DEVELOPMENT ACTIVITY:**\n\n"
                
                for jira_key in jira_keys:
                    related_commits = find_related_commits_for_jira_task(jira_key, all_commits)
                    
                    if related_commits:
                        bitbucket_info += f"**{jira_key} - Development History:**\n"
                        
                        # Group commits by repository
                        repo_commits = defaultdict(list)
                        for commit in related_commits:
                            repo_commits[commit['repository']].append(commit)
                        
                        for repo, commits in repo_commits.items():
                            bitbucket_info += f"• **{repo}** ({len(commits)} commits):\n"
                            
                            for commit in commits[:3]:  # Show max 3 commits per repo
                                # Create business-friendly description
                                business_desc = create_business_description(commit['message'])
                                bitbucket_info += f"  - {commit['date']}: {business_desc} (by {commit['author']})\n"
                            
                            if len(commits) > 3:
                                bitbucket_info += f"  ... and {len(commits) - 3} more commits\n"
                        
                        bitbucket_info += "\n"
                    else:
                        bitbucket_info += f"**{jira_key}** - No direct commits found in repositories\n\n"
                
                bitbucket_responses.append(bitbucket_info)
            else:
                # No JIRA keys found, but check if there's relevant activity
                simon_comment = str(row.get('Simon', ''))
                task_desc = str(row.get('Description', ''))
                
                if simon_comment and simon_comment.strip() and simon_comment != 'nan':
                    # Try to find contextually related commits
                    contextual_info = find_contextual_commits(simon_comment, task_desc, all_commits)
                    bitbucket_responses.append(contextual_info)
                else:
                    bitbucket_responses.append("")
        
        # Add Bitbucket column
        df['Bitbucket Development'] = bitbucket_responses
        
        # Translate all existing columns to English
        df = translate_columns_to_english(df)
        
        # Save enhanced file
        output_filename = f"Clockify_Enhanced_with_Bitbucket_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        with pd.ExcelWriter(output_filename, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Enhanced Report', index=False)
            
            # Adjust column widths
            worksheet = writer.sheets['Enhanced Report']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                
                # Special width for Bitbucket column
                if column[0].value == 'Bitbucket Development':
                    adjusted_width = min(max_length + 2, 120)
                else:
                    adjusted_width = min(max_length + 2, 50)
                
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        print(f"Enhanced Excel saved as: {output_filename}")
        return output_filename
        
    except Exception as e:
        print(f"Error enhancing Excel file: {e}")
        return None

def create_business_description(commit_message):
    """Converts technical commit message to business-friendly description"""
    
    message_lower = commit_message.lower()
    
    # Common patterns and their business translations
    patterns = {
        r'fix|bug|error|issue': 'Fixed system issue',
        r'add|new|create': 'Added new functionality',
        r'update|modify|change': 'Updated existing features',
        r'remove|delete': 'Removed obsolete components',
        r'refactor|clean': 'Improved code quality',
        r'test|spec': 'Enhanced testing coverage',
        r'config|setup': 'Updated system configuration',
        r'ui|interface|frontend': 'Improved user interface',
        r'api|backend|service': 'Enhanced backend services',
        r'database|db|sql': 'Updated data management',
        r'security|auth': 'Strengthened security measures',
        r'performance|optimize': 'Improved system performance',
        r'deploy|release': 'Prepared system deployment'
    }
    
    # Try to match patterns
    for pattern, description in patterns.items():
        if re.search(pattern, message_lower):
            return description
    
    # If no pattern matches, return cleaned up message
    clean_message = commit_message.split('\n')[0]  # Take first line only
    if len(clean_message) > 80:
        clean_message = clean_message[:80] + "..."
    
    return f"Development work: {clean_message}"

def find_contextual_commits(simon_comment, task_desc, all_commits):
    """Finds commits that might be contextually related to the task"""
    
    # Extract keywords from Simon's comment and task description
    keywords = []
    text_to_analyze = f"{simon_comment} {task_desc}".lower()
    
    # Extract meaningful words
    words = re.findall(r'\b\w+\b', text_to_analyze)
    keywords = [word for word in words if len(word) > 3 and word not in ['this', 'that', 'with', 'from', 'they', 'have', 'been', 'were']]
    
    if not keywords:
        return ""
    
    # Search for commits containing these keywords
    matching_commits = []
    
    for commit in all_commits:
        commit_text = commit['message'].lower()
        score = 0
        
        for keyword in keywords:
            if keyword in commit_text:
                score += 1
        
        if score > 0:
            matching_commits.append({
                'commit': commit,
                'score': score
            })
    
    # Sort by relevance score
    matching_commits.sort(key=lambda x: x['score'], reverse=True)
    
    if not matching_commits:
        return ""
    
    # Generate response
    response = "**POTENTIALLY RELATED DEVELOPMENT ACTIVITY:**\n\n"
    
    for match in matching_commits[:3]:  # Show top 3 matches
        commit = match['commit']
        business_desc = create_business_description(commit['message'])
        
        # Handle author field safely
        if 'author' in commit and commit['author']:
            if isinstance(commit['author'], dict):
                author = commit['author'].get('display_name', commit['author'].get('raw', 'Unknown'))
            else:
                author = str(commit['author'])
        else:
            author = 'Unknown'

        response += f"• **{commit['repository']}** ({commit['date'][:10]}):\n"
        response += f"  {business_desc} (by {author})\n"
        response += f"  Relevance score: {match['score']} keyword matches\n\n"
    
    response += f"**Keywords used for matching:** {', '.join(keywords[:10])}\n"
    
    return response

def translate_columns_to_english(df):
    """Translates column content to English"""
    
    # Column name translations
    column_translations = {
        'Projekt': 'Project',
        'Klient': 'Client', 
        'Opis': 'Description',
        'Zadanie': 'Task',
        'Użytkownik': 'User',
        'Grupa': 'Group',
        'Tagi': 'Tags',
        'Płatne': 'Billable',
        'Data rozpoczęcia': 'Start Date',
        'Godzina rozpoczęcia': 'Start Time',
        'Data zakończenia': 'End Date',
        'Godzina zakończenia': 'End Time',
        'Czas trwania (h)': 'Duration (h)',
        'Czas trwania (dziesiętny)': 'Duration (decimal)',
        'Stawka płatna (USD)': 'Billable Rate (USD)',
        'Kwota płatna (USD)': 'Billable Amount (USD)',
        'Kategoria': 'Category'
    }
    
    # Rename columns
    df = df.rename(columns=column_translations)
    
    # Translate content in specific columns
    content_translations = {
        'Bug': 'Bug',
        'Zadanie': 'Task',
        'Usprawnienie': 'Improvement',
        'Pytanie': 'Question',
        'Test': 'Test',
        'Tak': 'Yes',
        'Nie': 'No'
    }
    
    # Apply content translations to relevant columns
    for col in df.columns:
        if df[col].dtype == 'object':  # Only for text columns
            for polish, english in content_translations.items():
                df[col] = df[col].astype(str).str.replace(polish, english, regex=False)
    
    return df

def main():
    """Main function"""
    print("Starting comprehensive Bitbucket analysis for Trust project...")
    print("Analyzing last 3 months of development activity...")
    
    # Calculate date 3 months ago (with timezone awareness)
    from datetime import timezone
    three_months_ago = datetime.now(timezone.utc) - timedelta(days=90)
    print(f"Analyzing commits since: {three_months_ago.strftime('%Y-%m-%d')}")
    
    # Get commits from all repositories
    all_commits = []
    
    for repo in REPOSITORIES:
        print(f"\n=== Analyzing {repo} repository ===")
        commits = get_bitbucket_commits(repo, three_months_ago)
        all_commits.extend(commits)
        print(f"Found {len(commits)} commits in {repo}")
    
    print(f"\nTotal commits found: {len(all_commits)}")
    
    if not all_commits:
        print("No commits found in the specified period")
        return
    
    # Analyze commit activity
    print("\n=== Analyzing development activity ===")
    repo_stats, author_stats, jira_stats = analyze_commit_activity(all_commits)
    
    # Generate business summary
    business_summary = generate_business_summary(repo_stats, author_stats, jira_stats)
    
    # Save business summary
    summary_filename = f"Trust_Project_Development_Report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    with open(summary_filename, 'w', encoding='utf-8') as f:
        f.write(business_summary)
    
    print(f"Business summary saved as: {summary_filename}")
    
    # Enhance Excel file with Bitbucket data
    print("\n=== Enhancing Excel file with Bitbucket data ===")
    excel_file = "Clockify_FINAL_COMPLETE_ENGLISH.xlsx"
    
    enhanced_file = enhance_excel_with_bitbucket_data(excel_file, all_commits)
    
    if enhanced_file:
        print(f"\n✅ SUCCESS! Enhanced Excel file created: {enhanced_file}")
        print("\n📊 Analysis completed:")
        print(f"- {len(all_commits)} commits analyzed")
        print(f"- {len(repo_stats)} repositories processed")
        print(f"- {len(author_stats)} developers tracked")
        print(f"- {len(jira_stats)} JIRA tasks identified")
        print(f"- Business summary: {summary_filename}")
        print(f"- Enhanced Excel: {enhanced_file}")
    else:
        print("\n❌ Failed to enhance Excel file")
    
    # Display summary
    print("\n" + "="*80)
    print("BUSINESS SUMMARY:")
    print("="*80)
    print(business_summary)

if __name__ == "__main__":
    main()
