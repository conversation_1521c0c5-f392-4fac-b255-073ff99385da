**TRUST PROJECT DEVELOPMENT SUMMARY (Last 3 Months)**
• Total commits: 95
• Active developers: 4
• JIRA tasks addressed: 27
• Repositories updated: 3

**TRUST-NETCORE REPOSITORY:**
• Commits: 65
• Contributors: 2
• JIRA tasks: 13
• Recent activity:
  - 2025-05-05: Merged in release/24.04-v0.9 (pull request #844)

Release/24.04 v0.9
 (<PERSON><PERSON><PERSON> <<EMAIL>>)
  - 2025-05-05: Merge branch 'master' into release/24.04-v0.9

# Conflicts:
#	src/Trust.Application/ReadAtSeaServise... (DawidWitczak <<EMAIL>>)
  - 2025-04-28: Merge branch 'feature/RASEA-876' into release/24.04-v0.9
 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ak <<EMAIL>>)

**TRUST-REACT REPOSITORY:**
• Commits: 11
• Contributors: 2
• JIRA tasks: 15
• Recent activity:
  - 2025-05-05: Merged in release/24.04-v0.9 (pull request #510)

Release/24.04 v0.9

* Merge branch 'feature/RASEA-... (Dawid Witczak <<EMAIL>>)
  - 2025-04-24: Merge branch 'release/26.03-v0.9'
 (DawidWitczak <<EMAIL>>)
  - 2025-03-28: cherry pick
 (Dawid Witczak <<EMAIL>>)

**TRUST-AZURE-FUNCTIONS REPOSITORY:**
• Commits: 19
• Contributors: 2
• JIRA tasks: 5
• Recent activity:
  - 2025-05-08: hotfix FirstMeterReading (timestamp_ in emission report changed to set.FirstReadingTime to maintain ... (MKalinovski <<EMAIL>>)
  - 2025-05-07: hotfix cargo
 (MKalinovski <<EMAIL>>)
  - 2025-05-07: cargo fix
 (MKalinovski <<EMAIL>>)

**JIRA INTEGRATION INSIGHTS:**
• RASEA-876: 3 commits across 3 repositories
• RASEA-886: 4 commits across 2 repositories
• RASEA-890: 3 commits across 2 repositories
• RASEA-891: 1 commits across 1 repositories
• Refactors-02: 2 commits across 1 repositories
• RASEA-873: 3 commits across 2 repositories
• RASEA-880: 3 commits across 1 repositories
• RASEA-883: 2 commits across 1 repositories
• RASEA-850: 3 commits across 2 repositories
• RASEA-860: 3 commits across 1 repositories

**DEVELOPER ACTIVITY:**
• DawidWitczak <<EMAIL>>: 57 commits, 2 repositories, 11 JIRA tasks
• Dawid Witczak <<EMAIL>>: 19 commits, 2 repositories, 21 JIRA tasks
• MKalinovski <<EMAIL>>: 12 commits, 1 repositories, 0 JIRA tasks
• Mateusz Kalinowski <<EMAIL>>: 7 commits, 1 repositories, 5 JIRA tasks