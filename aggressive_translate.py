#!/usr/bin/env py
# -*- coding: utf-8 -*-

import pandas as pd
import re

def aggressive_translate_polish(text):
    """Aggressive translation targeting specific Polish phrases found in JIRA comments"""
    if pd.isna(text) or not isinstance(text, str):
        return text
    
    # Specific Polish phrases found in the data
    specific_translations = {
        # Complete phrases first
        'po spotkaniu': 'after the meeting',
        'Aplikujemy taką samą logikę kalkulacji dla Performance i Consumption': 'We apply the same calculation logic for Performance and Consumption',
        'W załączniku Simon podsyła results testowa': 'In the attachment Simon sends test results',
        'Wydaję mi się że dla mnie by był tylko 2 slide': 'It seems to me that for me there would be only 2 slides',
        '<PERSON><PERSON><PERSON> by była dla': 'The rest would be for',
        'daj proszę swoją opinie': 'please give your opinion',
        'Potrzebowalibyśmy testów czy issue dotyczy nowych danych': 'We would need tests whether the issue concerns new data',
        'istnieje szansa, że date no zostały zaktual': 'there is a chance that dates were not updated',
        'editing statków no działa': 'vessel editing does not work',
        'Albo po kliknięciu edit wywala stronę': 'Either after clicking edit it crashes the page',
        'albo jak już się uda wejść, to no zapisuja się zmiany': 'or when you manage to enter, changes are not saved',
        'Przetestowane przez Developera': 'Tested by Developer',
        'Utworzenie pop-up message po kliknięciu': 'Creating pop-up message after clicking',
        'z pytaniem o aktualny Vessel Status': 'with question about current Vessel Status',
        'information ta bedzie wysłana na backend': 'this information will be sent to backend',
        'oraz status bedzie wyświetlany autofilled w polu': 'and status will be displayed autofilled in the field',
        'Nadal no mamy żadnego śladu czemu notyfikacje no przychodzą': 'We still have no trace why notifications are not coming',
        'po naszej stronie all jest okej': 'on our side everything is okay',
        'Logika dodawani': 'Logic of adding',
        'Dodałam 2 tasks, które powinny załatwić kwestię braku wyświetlania notyfikacji': 'I added 2 tasks that should solve the issue of missing notification display',
        'mobilka-backend': 'mobile-backend',
        'Jak fixnięt': 'How to fix',
        'Odpowiedź na pytanie': 'Answer to the question',
        'komórka Y15 posiada 0.5, ponieważ komórki W15, X15 mają values 24, 1 which są zaokrągla': 'cell Y15 has 0.5, because cells W15, X15 have values 24, 1 which are rounded',
        'Czy zmiany dotyczące cargo w emission report są już wrzucone na UAT': 'Are changes regarding cargo in emission report already deployed to UAT',
        
        # Individual words and phrases
        'spotkaniu': 'meeting',
        'Aplikujemy': 'We apply',
        'taką samą': 'the same',
        'logikę': 'logic',
        'kalkulacji': 'calculation',
        'załączniku': 'attachment',
        'podsyła': 'sends',
        'testowa': 'test',
        'Wydaję': 'It seems',
        'się': 'to me',
        'że': 'that',
        'dla': 'for',
        'mnie': 'me',
        'był': 'would be',
        'tylko': 'only',
        'slide': 'slide',
        'Reszta': 'The rest',
        'była': 'would be',
        'daj': 'give',
        'proszę': 'please',
        'swoją': 'your',
        'opinie': 'opinion',
        'Potrzebowalibyśmy': 'We would need',
        'testów': 'tests',
        'czy': 'whether',
        'issue': 'issue',
        'dotyczy': 'concerns',
        'nowych': 'new',
        'danych': 'data',
        'istnieje': 'there is',
        'szansa': 'chance',
        'date': 'dates',
        'zostały': 'were',
        'zaktual': 'updated',
        'editing': 'editing',
        'statków': 'vessels',
        'działa': 'works',
        'Albo': 'Either',
        'po': 'after',
        'kliknięciu': 'clicking',
        'edit': 'edit',
        'wywala': 'crashes',
        'stronę': 'page',
        'albo': 'or',
        'jak': 'when',
        'już': 'already',
        'uda': 'manage',
        'wejść': 'to enter',
        'to': 'then',
        'no': 'not',
        'zapisuja': 'save',
        'zmiany': 'changes',
        'Przetestowane': 'Tested',
        'przez': 'by',
        'Developera': 'Developer',
        'Utworzenie': 'Creating',
        'message': 'message',
        'pytaniem': 'question',
        'o': 'about',
        'aktualny': 'current',
        'Status': 'Status',
        'information': 'information',
        'ta': 'this',
        'bedzie': 'will be',
        'wysłana': 'sent',
        'na': 'to',
        'backend': 'backend',
        'oraz': 'and',
        'status': 'status',
        'wyświetlany': 'displayed',
        'autofilled': 'autofilled',
        'w': 'in',
        'polu': 'field',
        'Nadal': 'Still',
        'mamy': 'we have',
        'żadnego': 'no',
        'śladu': 'trace',
        'czemu': 'why',
        'notyfikacje': 'notifications',
        'przychodzą': 'are coming',
        'naszej': 'our',
        'stronie': 'side',
        'all': 'everything',
        'jest': 'is',
        'okej': 'okay',
        'Logika': 'Logic',
        'dodawani': 'adding',
        'Dodałam': 'I added',
        'tasks': 'tasks',
        'które': 'which',
        'powinny': 'should',
        'załatwić': 'solve',
        'kwestię': 'issue',
        'braku': 'missing',
        'wyświetlania': 'display',
        'mobilka': 'mobile',
        'Jak': 'How',
        'fixnięt': 'to fix',
        'Odpowiedź': 'Answer',
        'pytanie': 'question',
        'komórka': 'cell',
        'posiada': 'has',
        'ponieważ': 'because',
        'komórki': 'cells',
        'mają': 'have',
        'values': 'values',
        'which': 'which',
        'są': 'are',
        'zaokrągla': 'rounded',
        'Czy': 'Are',
        'dotyczące': 'regarding',
        'cargo': 'cargo',
        'emission': 'emission',
        'report': 'report',
        'wrzucone': 'deployed',
        'UAT': 'UAT',
        
        # Common Polish words
        'i': 'and',
        'z': 'with',
        'do': 'to',
        'od': 'from',
        'za': 'for',
        'przed': 'before',
        'po': 'after',
        'nad': 'above',
        'pod': 'under',
        'przy': 'at',
        'bez': 'without',
        'dla': 'for',
        'podczas': 'during',
        'według': 'according to',
        'wokół': 'around',
        'między': 'between',
        'wśród': 'among',
        'przeciwko': 'against',
        'dzięki': 'thanks to',
        'mimo': 'despite',
        'oprócz': 'except',
        'zamiast': 'instead of',
        'obok': 'next to',
        'wzdłuż': 'along',
        'przez': 'through',
        'via': 'via',
        'co': 'what',
        'kto': 'who',
        'gdzie': 'where',
        'kiedy': 'when',
        'dlaczego': 'why',
        'jak': 'how',
        'ile': 'how much',
        'który': 'which',
        'jaki': 'what kind',
        'czyj': 'whose',
        'czy': 'whether',
        'jeśli': 'if',
        'gdy': 'when',
        'gdyby': 'if',
        'żeby': 'to',
        'aby': 'to',
        'bo': 'because',
        'więc': 'so',
        'ale': 'but',
        'jednak': 'however',
        'oraz': 'and',
        'albo': 'or',
        'ani': 'neither',
        'bądź': 'or',
        'czy': 'or',
        'lub': 'or',
        'tudzież': 'or',
        'oraz': 'as well as',
        'a': 'and',
        'lecz': 'but',
        'natomiast': 'while',
        'zaś': 'while',
        'tymczasem': 'meanwhile',
        'podczas gdy': 'while',
        'chociaż': 'although',
        'choć': 'though',
        'mimo że': 'despite',
        'pomimo': 'despite',
        'jednakże': 'however',
        'niemniej': 'nevertheless',
        'wszakże': 'however',
        'przecież': 'after all',
        'wprawdzie': 'admittedly',
        'owszem': 'indeed',
        'rzeczywiście': 'indeed',
        'faktycznie': 'actually',
        'naprawdę': 'really',
        'prawdziwie': 'truly',
        'istotnie': 'indeed',
        'oczywiście': 'obviously',
        'naturalnie': 'naturally',
        'bezsprzecznie': 'undoubtedly',
        'niewątpliwie': 'undoubtedly',
        'z pewnością': 'certainly',
        'na pewno': 'for sure',
        'bez wątpienia': 'without doubt',
        'zapewne': 'probably',
        'prawdopodobnie': 'probably',
        'możliwe': 'possible',
        'być może': 'maybe',
        'chyba': 'probably',
        'bodajże': 'perhaps',
        'podobno': 'supposedly',
        'rzekomo': 'allegedly',
        'jakoby': 'supposedly',
        'niby': 'supposedly',
        'ponoć': 'supposedly'
    }
    
    result = str(text)
    
    # Apply translations in order of length (longest first)
    for polish, english in sorted(specific_translations.items(), key=lambda x: len(x[0]), reverse=True):
        # Use case-insensitive replacement
        result = re.sub(re.escape(polish), english, result, flags=re.IGNORECASE)
    
    # Additional cleanup for Polish characters
    polish_chars = {
        'ą': 'a', 'ć': 'c', 'ę': 'e', 'ł': 'l', 'ń': 'n', 
        'ó': 'o', 'ś': 's', 'ź': 'z', 'ż': 'z',
        'Ą': 'A', 'Ć': 'C', 'Ę': 'E', 'Ł': 'L', 'Ń': 'N',
        'Ó': 'O', 'Ś': 'S', 'Ź': 'Z', 'Ż': 'Z'
    }
    
    for polish_char, english_char in polish_chars.items():
        result = result.replace(polish_char, english_char)
    
    return result

def main():
    """Main function for aggressive Polish translation"""
    
    filename = "Clockify_FINAL_Professional_Simon_Analysis.xlsx"
    
    print("Loading Excel file for aggressive Polish translation...")
    
    try:
        # Load the Excel file
        df = pd.read_excel(filename, sheet_name='Complete Analysis')
        print(f"Loaded {len(df)} rows and {len(df.columns)} columns")
        
        # Focus on JIRA Information column which contains most Polish content
        print("Applying aggressive translation to JIRA Information column...")
        
        if 'JIRA Information' in df.columns:
            df['JIRA Information'] = df['JIRA Information'].apply(aggressive_translate_polish)
            print("JIRA Information column translated")
        
        # Also translate other text columns
        text_columns = ['Description', 'Task', 'Simon', 'Bitbucket Development', 'Comprehensive Analysis']
        for col in text_columns:
            if col in df.columns:
                print(f"Translating column: {col}")
                df[col] = df[col].apply(aggressive_translate_polish)
        
        # Save the aggressively translated file
        df.to_excel(filename, sheet_name='Complete Analysis', index=False)
        print(f"Aggressive translation completed and saved to: {filename}")
        
        # Check for remaining Polish content
        print("\nChecking for remaining Polish content...")
        
        # Look for specific Polish indicators
        polish_indicators = [
            'ą', 'ć', 'ę', 'ł', 'ń', 'ó', 'ś', 'ź', 'ż',  # Polish characters
            'się', 'że', 'przez', 'które', 'dla', 'po', 'na', 'w', 'z', 'do', 'od'  # Common Polish words
        ]
        
        remaining_count = 0
        for col in df.columns:
            if df[col].dtype == 'object':
                for indicator in polish_indicators:
                    count = df[col].astype(str).str.contains(indicator, case=False, na=False).sum()
                    if count > 0:
                        remaining_count += count
        
        print(f"Remaining Polish indicators found: {remaining_count}")
        
        # Show examples of translated JIRA content
        print("\n=== EXAMPLES OF TRANSLATED JIRA CONTENT ===")
        
        jira_examples = 0
        for idx, row in df.iterrows():
            jira_info = str(row.get('JIRA Information', ''))
            if 'Comments' in jira_info and len(jira_info) > 500:
                print(f"\nJIRA Translation Example {jira_examples + 1} (Row {idx+1}):")
                print(jira_info[:600] + "...")
                jira_examples += 1
                if jira_examples >= 2:  # Show 2 examples
                    break
        
        print(f"\n✅ Aggressive translation completed!")
        print(f"📊 Summary:")
        print(f"- File processed: {filename}")
        print(f"- Rows: {len(df)}")
        print(f"- Columns: {len(df.columns)}")
        print(f"- Remaining Polish indicators: {remaining_count}")
        
        return filename
        
    except Exception as e:
        print(f"Error processing file: {e}")
        return None

if __name__ == "__main__":
    main()
