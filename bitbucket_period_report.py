#!/usr/bin/env py
# -*- coding: utf-8 -*-

import pandas as pd
import requests
import json
import base64
import re
from datetime import datetime, timedelta, timezone
from collections import defaultdict

# Bitbucket Configuration
BITBUCKET_USERNAME = "mkasoft"
BITBUCKET_APP_PASSWORD = "ATBBspuHWSzDP9kUnNBRJWbLyGAP7E2DE5BA"
WORKSPACE = "a-softy"

# Repositories to analyze
REPOSITORIES = [
    "trust-netcore",
    "trust-react", 
    "trust-azure-functions"
]

def get_bitbucket_commits_period(repo_name, start_date, end_date):
    """Retrieves commits from Bitbucket repository for specific period"""
    
    auth_string = f"{BITBUCKET_USERNAME}:{BITBUCKET_APP_PASSWORD}"
    auth_bytes = auth_string.encode('ascii')
    auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
    
    headers = {
        'Authorization': f'Basic {auth_b64}',
        'Accept': 'application/json'
    }
    
    all_commits = []
    url = f"https://api.bitbucket.org/2.0/repositories/{WORKSPACE}/{repo_name}/commits/master"
    
    while url:
        try:
            print(f"Fetching commits from {repo_name}...")
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            data = response.json()
            
            commits = data.get('values', [])
            
            for commit in commits:
                commit_date = datetime.fromisoformat(commit['date'].replace('Z', '+00:00'))
                
                # Filter by date range
                if commit_date < start_date:
                    return all_commits  # Stop when we reach older commits
                
                if start_date <= commit_date <= end_date:
                    # Add repository info to commit
                    commit['repository'] = repo_name
                    all_commits.append(commit)
            
            # Get next page
            url = data.get('next')
            
        except requests.exceptions.RequestException as e:
            print(f"Error fetching commits from {repo_name}: {e}")
            break
    
    return all_commits

def extract_jira_keys_from_commit(commit_message):
    """Extracts JIRA keys from commit message"""
    patterns = [
        r'\b(RASEA-\d+)\b',
        r'\b(RS-\d+)\b',
        r'\b([A-Z]+-\d+)\b'
    ]
    
    jira_keys = []
    for pattern in patterns:
        matches = re.findall(pattern, commit_message, re.IGNORECASE)
        jira_keys.extend(matches)
    
    return list(set(jira_keys))

def create_business_description(commit_message, jira_keys):
    """Converts technical commit message to business-friendly description"""
    
    message_lower = commit_message.lower()
    
    # Enhanced business patterns with more specific descriptions
    patterns = {
        r'fix|bug|error|issue|hotfix': 'System Issue Resolution',
        r'add|new|create|implement': 'New Feature Implementation',
        r'update|modify|change|improve': 'Feature Enhancement',
        r'remove|delete|clean': 'Code Optimization',
        r'refactor|restructure': 'Code Quality Improvement',
        r'test|spec|unit': 'Quality Assurance Enhancement',
        r'config|setup|deploy': 'System Configuration Update',
        r'ui|interface|frontend|react': 'User Interface Improvement',
        r'api|backend|service|netcore': 'Backend Service Enhancement',
        r'database|db|sql|data': 'Data Management Update',
        r'security|auth|login': 'Security Enhancement',
        r'performance|optimize|speed': 'Performance Optimization',
        r'merge|pull request|pr': 'Code Integration',
        r'release|version|build': 'Release Preparation',
        r'notification|email|alert': 'Communication System Update',
        r'report|export|download': 'Reporting Feature Enhancement',
        r'validation|verify|check': 'Data Validation Improvement',
        r'cargo|vessel|ship': 'Maritime Operations Feature',
        r'emission|environmental': 'Environmental Compliance Feature',
        r'meter|reading|measurement': 'Measurement System Enhancement'
    }
    
    # Try to match patterns
    for pattern, description in patterns.items():
        if re.search(pattern, message_lower):
            # Add JIRA context if available
            if jira_keys:
                return f"{description} (Task: {', '.join(jira_keys)})"
            return description
    
    # If no pattern matches, create generic business description
    clean_message = commit_message.split('\n')[0]  # Take first line only
    if len(clean_message) > 60:
        clean_message = clean_message[:60] + "..."
    
    if jira_keys:
        return f"Development Work: {clean_message} (Task: {', '.join(jira_keys)})"
    return f"Development Work: {clean_message}"

def categorize_work_type(commit_message, jira_keys):
    """Categorizes the type of work based on commit message"""
    
    message_lower = commit_message.lower()
    
    # Work type categories
    if re.search(r'fix|bug|error|issue|hotfix', message_lower):
        return "Bug Fix"
    elif re.search(r'add|new|create|implement|feature', message_lower):
        return "New Feature"
    elif re.search(r'update|modify|change|improve|enhance', message_lower):
        return "Enhancement"
    elif re.search(r'refactor|clean|optimize|restructure', message_lower):
        return "Code Quality"
    elif re.search(r'test|spec|unit|qa', message_lower):
        return "Testing"
    elif re.search(r'config|setup|deploy|release', message_lower):
        return "Deployment"
    elif re.search(r'merge|pull request|pr', message_lower):
        return "Integration"
    elif re.search(r'ui|interface|frontend', message_lower):
        return "UI/UX"
    elif re.search(r'api|backend|service', message_lower):
        return "Backend"
    elif re.search(r'database|db|data', message_lower):
        return "Data Management"
    else:
        return "General Development"

def estimate_complexity(commit_message, files_changed=None):
    """Estimates complexity based on commit message and context"""
    
    message_lower = commit_message.lower()
    
    # High complexity indicators
    if re.search(r'refactor|restructure|major|breaking|migration', message_lower):
        return "High"
    elif re.search(r'merge|release|integration|multiple', message_lower):
        return "Medium"
    elif re.search(r'hotfix|quick|minor|small|typo', message_lower):
        return "Low"
    else:
        return "Medium"

def determine_business_impact(commit_message, jira_keys, work_type):
    """Determines business impact of the commit"""
    
    message_lower = commit_message.lower()
    
    # High impact indicators
    if re.search(r'security|critical|production|hotfix|urgent', message_lower):
        return "High - Critical System Update"
    elif re.search(r'performance|optimization|speed|efficiency', message_lower):
        return "High - Performance Improvement"
    elif re.search(r'new|feature|functionality|capability', message_lower):
        return "Medium - New Capability"
    elif re.search(r'ui|interface|user|experience', message_lower):
        return "Medium - User Experience"
    elif re.search(r'bug|fix|error|issue', message_lower):
        return "Medium - System Reliability"
    elif re.search(r'test|quality|validation', message_lower):
        return "Low - Quality Assurance"
    elif re.search(r'refactor|clean|code', message_lower):
        return "Low - Code Maintenance"
    else:
        return "Medium - System Enhancement"

def create_period_report(commits, start_date, end_date):
    """Creates Excel report for the specified period"""
    
    print(f"Creating report for period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
    print(f"Processing {len(commits)} commits...")
    
    # Prepare data for Excel
    report_data = []
    
    for commit in commits:
        # Handle author field safely
        if 'author' in commit and commit['author']:
            if isinstance(commit['author'], dict):
                author = commit['author'].get('display_name', commit['author'].get('raw', 'Unknown'))
            else:
                author = str(commit['author'])
        else:
            author = 'Unknown'
        
        # Extract commit information
        commit_date = datetime.fromisoformat(commit['date'].replace('Z', '+00:00'))
        message = commit.get('message', '')
        repository = commit.get('repository', '')
        commit_hash = commit.get('hash', commit.get('node', 'unknown'))[:8]
        
        # Extract JIRA keys from FULL message (not just first line!)
        jira_keys = extract_jira_keys_from_commit(message)

        # Generate business descriptions
        business_description = create_business_description(message, jira_keys)
        work_type = categorize_work_type(message, jira_keys)
        complexity = estimate_complexity(message)
        business_impact = determine_business_impact(message, jira_keys, work_type)

        # Clean up commit message for display (first line only for readability)
        clean_message = message.split('\n')[0]  # First line only
        if len(clean_message) > 100:
            clean_message = clean_message[:100] + "..."
        
        report_data.append({
            'Date': commit_date.strftime('%Y-%m-%d'),
            'Time': commit_date.strftime('%H:%M'),
            'Repository': repository.replace('trust-', '').title(),
            'Developer': author.split('<')[0].strip(),  # Remove email part
            'Work Type': work_type,
            'Business Description': business_description,
            'Business Impact': business_impact,
            'Complexity': complexity,
            'JIRA Tasks': ', '.join(jira_keys) if jira_keys else 'No JIRA reference',
            'Technical Details': clean_message,
            'Commit ID': commit_hash
        })
    
    # Create DataFrame
    df = pd.DataFrame(report_data)
    
    # Sort by date (newest first)
    df = df.sort_values('Date', ascending=False)
    
    # Create summary statistics
    summary_data = create_summary_statistics(df, start_date, end_date)
    
    # Save to Excel with multiple sheets
    output_filename = f"Trust_Development_Report_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.xlsx"
    
    with pd.ExcelWriter(output_filename, engine='openpyxl') as writer:
        # Main report sheet
        df.to_excel(writer, sheet_name='Development Activity', index=False)
        
        # Summary sheet
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='Executive Summary', index=False)
        
        # Adjust column widths for main sheet
        worksheet = writer.sheets['Development Activity']
        column_widths = {
            'A': 12,  # Date
            'B': 8,   # Time
            'C': 15,  # Repository
            'D': 20,  # Developer
            'E': 18,  # Work Type
            'F': 50,  # Business Description
            'G': 35,  # Business Impact
            'H': 12,  # Complexity
            'I': 25,  # JIRA Tasks
            'J': 60,  # Technical Details
            'K': 12   # Commit ID
        }
        
        for col, width in column_widths.items():
            worksheet.column_dimensions[col].width = width
        
        # Adjust column widths for summary sheet
        summary_worksheet = writer.sheets['Executive Summary']
        for column in summary_worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            summary_worksheet.column_dimensions[column_letter].width = adjusted_width
    
    print(f"Report saved as: {output_filename}")
    return output_filename, df

def create_summary_statistics(df, start_date, end_date):
    """Creates executive summary statistics"""
    
    summary_data = []
    
    # Period information
    summary_data.append({
        'Metric': 'Reporting Period',
        'Value': f"{start_date.strftime('%B %d, %Y')} - {end_date.strftime('%B %d, %Y')}",
        'Description': 'Development activity analysis period'
    })
    
    summary_data.append({
        'Metric': 'Total Commits',
        'Value': len(df),
        'Description': 'Number of code changes delivered'
    })
    
    # Repository breakdown
    repo_counts = df['Repository'].value_counts()
    for repo, count in repo_counts.items():
        summary_data.append({
            'Metric': f'{repo} Repository',
            'Value': f"{count} commits ({count/len(df)*100:.1f}%)",
            'Description': f'Development activity in {repo} codebase'
        })
    
    # Developer productivity
    dev_counts = df['Developer'].value_counts()
    summary_data.append({
        'Metric': 'Active Developers',
        'Value': len(dev_counts),
        'Description': 'Number of team members contributing code'
    })
    
    for dev, count in dev_counts.items():
        summary_data.append({
            'Metric': f'{dev} Productivity',
            'Value': f"{count} commits ({count/len(df)*100:.1f}%)",
            'Description': f'Individual contribution to project development'
        })
    
    # Work type analysis
    work_type_counts = df['Work Type'].value_counts()
    for work_type, count in work_type_counts.items():
        summary_data.append({
            'Metric': f'{work_type} Work',
            'Value': f"{count} commits ({count/len(df)*100:.1f}%)",
            'Description': f'Development effort focused on {work_type.lower()}'
        })
    
    # Business impact analysis
    impact_counts = df['Business Impact'].value_counts()
    high_impact = sum(count for impact, count in impact_counts.items() if impact.startswith('High'))
    medium_impact = sum(count for impact, count in impact_counts.items() if impact.startswith('Medium'))
    low_impact = sum(count for impact, count in impact_counts.items() if impact.startswith('Low'))
    
    summary_data.append({
        'Metric': 'High Impact Changes',
        'Value': f"{high_impact} commits ({high_impact/len(df)*100:.1f}%)",
        'Description': 'Critical system updates and performance improvements'
    })
    
    summary_data.append({
        'Metric': 'Medium Impact Changes',
        'Value': f"{medium_impact} commits ({medium_impact/len(df)*100:.1f}%)",
        'Description': 'New features and system reliability improvements'
    })
    
    summary_data.append({
        'Metric': 'Low Impact Changes',
        'Value': f"{low_impact} commits ({low_impact/len(df)*100:.1f}%)",
        'Description': 'Code maintenance and quality assurance'
    })
    
    # JIRA integration
    jira_commits = len(df[df['JIRA Tasks'] != 'No JIRA reference'])
    summary_data.append({
        'Metric': 'JIRA Integration',
        'Value': f"{jira_commits} commits ({jira_commits/len(df)*100:.1f}%)",
        'Description': 'Development work linked to project management tasks'
    })
    
    # Complexity analysis
    complexity_counts = df['Complexity'].value_counts()
    for complexity, count in complexity_counts.items():
        summary_data.append({
            'Metric': f'{complexity} Complexity Work',
            'Value': f"{count} commits ({count/len(df)*100:.1f}%)",
            'Description': f'Development tasks with {complexity.lower()} implementation complexity'
        })
    
    return summary_data

def main():
    """Main function"""
    print("Creating Trust Project Development Report for specified period...")
    
    # Define date range: April 16, 2025 to May 31, 2025
    start_date = datetime(2025, 4, 16, tzinfo=timezone.utc)
    end_date = datetime(2025, 5, 31, 23, 59, 59, tzinfo=timezone.utc)
    
    print(f"Analyzing period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
    
    # Get commits from all repositories for the specified period
    all_commits = []
    
    for repo in REPOSITORIES:
        print(f"\n=== Analyzing {repo} repository ===")
        commits = get_bitbucket_commits_period(repo, start_date, end_date)
        all_commits.extend(commits)
        print(f"Found {len(commits)} commits in {repo} for the specified period")
    
    print(f"\nTotal commits found in period: {len(all_commits)}")
    
    if not all_commits:
        print("No commits found in the specified period")
        return
    
    # Create Excel report
    print("\n=== Creating Excel Report ===")
    output_file, df = create_period_report(all_commits, start_date, end_date)
    
    if output_file:
        print(f"\n✅ SUCCESS! Excel report created: {output_file}")
        print(f"\n📊 Report Summary:")
        print(f"- Period: April 16, 2025 - May 31, 2025")
        print(f"- Total commits: {len(df)}")
        print(f"- Repositories: {', '.join(df['Repository'].unique())}")
        print(f"- Developers: {', '.join(df['Developer'].unique())}")
        print(f"- Work types: {', '.join(df['Work Type'].unique())}")
        
        # Show top business activities
        print(f"\n🎯 Top Business Activities:")
        work_type_counts = df['Work Type'].value_counts()
        for work_type, count in work_type_counts.head(5).items():
            print(f"- {work_type}: {count} commits ({count/len(df)*100:.1f}%)")
    else:
        print("\n❌ Failed to create Excel report")

if __name__ == "__main__":
    main()
