#!/usr/bin/env py
# -*- coding: utf-8 -*-

import pandas as pd
import requests
import json
import base64
import re
from datetime import datetime

# JIRA Configuration
JIRA_URL = "https://read-at-sea.atlassian.net"
EMAIL = "j.w<PERSON><PERSON><PERSON>@a-soft.pl"
API_TOKEN = "ATATT3xFfGF0o1tVy4he6mvThd2U0mZU2NCqHbZoCB216R6aPWZZUnKecNaRaTIFIBkmb3sTHDvEPtjnJ__aW1EGolFwrIFPuo5qY9Z4SEW2TlMMt8d8v0sLWKkGGMkjLSDQQyP2wT4BImUn_XaNGozE1-KzMj1GFxhAtdkdmwtMErw0hkwPo4Q=D1D6CC05"

def get_jira_issue_with_comments(issue_key):
    """Retrieves JIRA issue details with comments"""
    
    # Prepare authorization
    auth_string = f"{EMAIL}:{API_TOKEN}"
    auth_bytes = auth_string.encode('ascii')
    auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
    
    headers = {
        'Authorization': f'Basic {auth_b64}',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }
    
    # Get basic issue information
    url = f"{JIRA_URL}/rest/api/3/issue/{issue_key}"
    params = {
        'fields': 'key,summary,status,assignee,reporter,created,updated,priority,issuetype,description,components,labels,comment'
    }
    
    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        data = response.json()
        
        # Process description from JIRA format to text
        if data.get('fields', {}).get('description'):
            description_content = data['fields']['description']
            data['fields']['description_text'] = extract_text_from_jira_content(description_content)
            data['fields']['description_raw'] = description_content
        
        # Process comments
        comments_text = []
        if data.get('fields', {}).get('comment', {}).get('comments'):
            for comment in data['fields']['comment']['comments']:
                if comment.get('body'):
                    comment_text = extract_text_from_jira_content(comment['body'])
                    author = comment.get('author', {}).get('displayName', 'Unknown')
                    created = comment.get('created', '')
                    comments_text.append(f"[{author}, {created[:10]}]: {comment_text}")
        
        data['fields']['comments_text'] = comments_text
        data['fields']['all_comments_combined'] = ' | '.join(comments_text)
        
        return data
    except requests.exceptions.RequestException as e:
        print(f"Error retrieving issue {issue_key}: {e}")
        return None

def extract_text_from_jira_content(content):
    """Extracts text from JIRA content structure (JSON)"""
    if not content:
        return ""
    
    if isinstance(content, str):
        return content
    
    if isinstance(content, dict):
        text_parts = []
        
        # Check if it's Atlassian Document Format structure
        if content.get('type') == 'doc' and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))
        
        # Check other element types
        elif content.get('type') == 'paragraph' and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))
        
        elif content.get('type') == 'text' and 'text' in content:
            text_parts.append(content['text'])
        
        elif content.get('type') in ['heading', 'codeBlock', 'blockquote'] and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))
        
        # If it's a list
        elif content.get('type') in ['bulletList', 'orderedList'] and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))
        
        elif content.get('type') == 'listItem' and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))
        
        return ' '.join(text_parts)
    
    elif isinstance(content, list):
        text_parts = []
        for item in content:
            text_parts.append(extract_text_from_jira_content(item))
        return ' '.join(text_parts)
    
    return str(content)

def extract_jira_keys(text):
    """Extracts JIRA numbers from text, including from links"""
    if pd.isna(text) or not isinstance(text, str):
        return []
    
    jira_keys = []
    
    # 1. Find direct JIRA numbers
    patterns = [
        r'\b(RS-\d+)\b',
        r'\b(RASEA-\d+)\b',
        r'\b([A-Z]+-\d+)\b'
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        jira_keys.extend(matches)
    
    return list(set(jira_keys))  # Remove duplicates

def get_all_jira_projects_issues():
    """Retrieves issues from both RASEA and RS projects"""
    
    # Prepare authorization
    auth_string = f"{EMAIL}:{API_TOKEN}"
    auth_bytes = auth_string.encode('ascii')
    auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
    
    headers = {
        'Authorization': f'Basic {auth_b64}',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }
    
    all_issues = {}
    
    # Get issues from both projects
    projects = ['RASEA', 'RS']
    
    for project in projects:
        print(f"Retrieving issues from project {project}...")
        
        url = f"{JIRA_URL}/rest/api/3/search"
        start_at = 0
        max_results = 50
        
        while True:
            params = {
                'jql': f'project = {project} ORDER BY created DESC',
                'startAt': start_at,
                'maxResults': max_results,
                'fields': 'key,summary,status,assignee,reporter,created,updated,priority,issuetype,description,components,labels,comment'
            }
            
            try:
                response = requests.get(url, headers=headers, params=params)
                response.raise_for_status()
                data = response.json()
                
                issues = data.get('issues', [])
                
                for issue in issues:
                    issue_key = issue['key']
                    fields = issue.get('fields', {})
                    
                    # Process description
                    if fields.get('description'):
                        fields['description_text'] = extract_text_from_jira_content(fields['description'])
                    
                    # Process comments
                    comments_text = []
                    if fields.get('comment', {}).get('comments'):
                        for comment in fields['comment']['comments']:
                            if comment.get('body'):
                                comment_text = extract_text_from_jira_content(comment['body'])
                                author = comment.get('author', {}).get('displayName', 'Unknown')
                                created = comment.get('created', '')
                                comments_text.append(f"[{author}, {created[:10]}]: {comment_text}")
                    
                    fields['comments_text'] = comments_text
                    fields['all_comments_combined'] = ' | '.join(comments_text)
                    
                    all_issues[issue_key] = fields
                
                # Check if there are more issues to retrieve
                total = data.get('total', 0)
                print(f"Retrieved {len(issues)} issues from {project}, total: {len([k for k in all_issues.keys() if k.startswith(project)])}/{total}")
                
                if start_at + len(issues) >= total or len(issues) == 0:
                    break
                    
                start_at += max_results
                
            except requests.exceptions.RequestException as e:
                print(f"Error retrieving data from JIRA project {project}: {e}")
                break
    
    return all_issues

def classify_issue_type(simon_comment, description_text, task_text, row_data, jira_details):
    """Classifies issue type based on all available data"""
    
    # Combine all text for analysis
    all_text = f"{simon_comment} {description_text} {task_text}".lower()
    
    # Add data from row
    if row_data:
        for col, value in row_data.items():
            if pd.notna(value) and isinstance(value, str):
                all_text += f" {value}".lower()
    
    # Add data from JIRA
    jira_text = ""
    jira_types = []
    jira_descriptions = []
    jira_comments = []
    jira_sources = []
    
    for jira_key, fields in jira_details.items():
        jira_sources.append(jira_key)
        
        if fields.get('summary'):
            jira_text += f" {fields['summary']}".lower()
        if fields.get('description_text'):
            jira_text += f" {fields['description_text']}".lower()
            jira_descriptions.append(f"[{jira_key}] {fields['description_text']}")
        if fields.get('all_comments_combined'):
            jira_text += f" {fields['all_comments_combined']}".lower()
            jira_comments.append(f"[{jira_key}] {fields['all_comments_combined']}")
        if fields.get('issuetype', {}).get('name'):
            jira_types.append(fields['issuetype']['name'].lower())
        if fields.get('labels'):
            for label in fields['labels']:
                jira_text += f" {label}".lower()
    
    all_text += jira_text
    
    # Keywords for classification
    bug_keywords = ['bug', 'error', 'issue', 'problem', 'fix', 'broken', 'not working', 'fail', 'crash', 'incorrect', 'wrong', 'missing']
    improvement_keywords = ['improvement', 'enhance', 'feature', 'add', 'new', 'change', 'request', 'update', 'upgrade', 'better', 'optimize']
    question_keywords = ['explain', 'clarify', 'understand', 'what', 'how', 'why', 'question', '?']
    test_keywords = ['test', 'testing', 'verify', 'check', 'validation', 'qa']
    
    # Calculate scores
    bug_score = sum(1 for keyword in bug_keywords if keyword in all_text)
    improvement_score = sum(1 for keyword in improvement_keywords if keyword in all_text)
    question_score = sum(1 for keyword in question_keywords if keyword in all_text)
    test_score = sum(1 for keyword in test_keywords if keyword in all_text)
    
    # Determine issue type
    if any('bug' in jtype for jtype in jira_types) or bug_score >= improvement_score:
        issue_type = 'bug'
    elif any('story' in jtype or 'feature' in jtype or 'improvement' in jtype for jtype in jira_types) or improvement_score > bug_score:
        issue_type = 'improvement'
    elif question_score > max(bug_score, improvement_score):
        issue_type = 'question'
    elif test_score > max(bug_score, improvement_score):
        issue_type = 'test'
    else:
        issue_type = 'general'
    
    # Prepare response
    if issue_type == 'bug':
        header = "🐛 **BUG** - "
        explanation = f"Classified as BUG based on:\n"
        explanation += f"- Keywords indicating error (score: {bug_score})\n"
        if jira_sources:
            explanation += f"- Data sources: {', '.join(jira_sources)}\n"
        if any('bug' in jtype for jtype in jira_types):
            explanation += f"- JIRA task type: {', '.join(jira_types)}\n"
        if jira_descriptions:
            explanation += f"- JIRA descriptions: {' | '.join(jira_descriptions[:2])}\n"
        if jira_comments:
            explanation += f"- JIRA comments: {' | '.join(jira_comments[:2])}\n"
        explanation += f"- Context indicates system malfunction\n"
        explanation += f"- Requires fixing existing functionality"
        
        recommendations = """1. Reproduce the bug in test environment
2. Identify root cause (root cause analysis)
3. Develop and test the fix
4. Perform regression testing
5. Deploy to production with appropriate tests"""
        
    elif issue_type == 'improvement':
        header = "✨ **IMPROVEMENT** - "
        explanation = f"Classified as IMPROVEMENT based on:\n"
        explanation += f"- Keywords indicating enhancement (score: {improvement_score})\n"
        if jira_sources:
            explanation += f"- Data sources: {', '.join(jira_sources)}\n"
        if any('story' in jtype or 'feature' in jtype or 'improvement' in jtype for jtype in jira_types):
            explanation += f"- JIRA task type: {', '.join(jira_types)}\n"
        if jira_descriptions:
            explanation += f"- JIRA descriptions: {' | '.join(jira_descriptions[:2])}\n"
        if jira_comments:
            explanation += f"- JIRA comments: {' | '.join(jira_comments[:2])}\n"
        explanation += f"- Context indicates adding new functionality\n"
        explanation += f"- Relates to extending or improving existing capabilities"
        
        recommendations = """1. Analyze impact on existing functionality
2. Estimate effort and resources required
3. Plan implementation in appropriate sprint
4. Prepare acceptance tests and documentation
5. Consult with UX/UI team if needed"""
        
    elif issue_type == 'question':
        header = "❓ **QUESTION** - "
        explanation = f"Classified as QUESTION based on:\n"
        explanation += f"- Keywords indicating question (score: {question_score})\n"
        explanation += f"- Context requires clarification or additional information\n"
        explanation += f"- Need to understand requirements or functionality"
        
        recommendations = """1. Analyze the question in detail
2. Gather additional information if needed
3. Consult with team or experts
4. Prepare detailed response
5. Document solution for future reference"""
        
    elif issue_type == 'test':
        header = "🧪 **TESTING** - "
        explanation = f"Classified as TESTING based on:\n"
        explanation += f"- Keywords indicating testing (score: {test_score})\n"
        explanation += f"- Context requires verification or checking\n"
        explanation += f"- Related to quality control or acceptance"
        
        recommendations = """1. Prepare test plan
2. Execute functional and non-functional tests
3. Document test results
4. Report any bugs or issues found
5. Confirm functionality acceptance"""
        
    else:
        header = "📝 **NOTE** - "
        explanation = f"Requires additional analysis:\n"
        explanation += f"- No clear type indicators (bug: {bug_score}, improvement: {improvement_score})\n"
        explanation += f"- Context requires deeper analysis\n"
        explanation += f"- May require team consultation"
        
        recommendations = """1. Analyze context in detail
2. Consult with team or client
3. Determine priority and action type
4. Plan appropriate steps
5. Monitor progress and results"""
    
    return {
        'header': header,
        'explanation': explanation,
        'recommendations': recommendations
    }

def analyze_row_complete(simon_comment, description_text="", task_text="", row_data=None, jira_keys_in_row=None, all_jira_keys_in_file=None, all_jira_details=None, current_row_number=None):
    """Analyzes row and generates all 4 columns of data"""

    if jira_keys_in_row is None:
        jira_keys_in_row = []
    if all_jira_keys_in_file is None:
        all_jira_keys_in_file = {}
    if all_jira_details is None:
        all_jira_details = {}

    # If no JIRA numbers and no Simon comment, return empty responses
    if not jira_keys_in_row and (pd.isna(simon_comment) or not isinstance(simon_comment, str) or not simon_comment.strip()):
        return {"source_data": "", "jira_details": "", "analysis": "", "rs_service": ""}

    # COLUMN 1: SOURCE DATA
    source_data = "**SOURCE DATA USED FOR ANALYSIS:**\n\n"

    if simon_comment and simon_comment.strip():
        source_data += f"• **Simon's Comment:** {simon_comment}\n"

    if description_text.strip():
        source_data += f"• **Task Description:** {description_text.strip()}\n"
    if task_text.strip():
        source_data += f"• **Task Name:** {task_text.strip()}\n"

    # Add other relevant data from row
    if row_data:
        for col, value in row_data.items():
            if pd.notna(value) and isinstance(value, str) and value.strip():
                col_lower = str(col).lower()
                if any(word in col_lower for word in ['project', 'client', 'category', 'tags']):
                    source_data += f"• **{col}:** {value}\n"

    if jira_keys_in_row:
        source_data += f"• **Found JIRA Numbers:** {', '.join(jira_keys_in_row)}\n"

    # COLUMN 2: JIRA DETAILS (RASEA + RS)
    jira_details = ""

    if jira_keys_in_row:
        jira_details = "**JIRA TASK DETAILS (RASEA + RS PROJECTS):**\n\n"

        for jira_key in jira_keys_in_row:
            # Check if this is the first occurrence of this key in the file
            first_occurrence_rows = all_jira_keys_in_file.get(jira_key, [])
            first_row = min(first_occurrence_rows) if first_occurrence_rows else current_row_number

            if first_row < current_row_number:
                # This is a duplicate
                jira_details += f"**{jira_key}** - SEE DETAILS IN ROW {first_row}\n\n"
            else:
                # This is the first occurrence - show full details
                if jira_key in all_jira_details:
                    fields = all_jira_details[jira_key]

                    # Determine project
                    project = "RASEA" if jira_key.startswith("RASEA") else "RS"

                    jira_details += f"**{jira_key}** (Project: {project})\n"
                    jira_details += f"• **Title:** {fields.get('summary', 'No title')}\n"
                    jira_details += f"• **Status:** {fields.get('status', {}).get('name', 'Unknown')}\n"
                    jira_details += f"• **Type:** {fields.get('issuetype', {}).get('name', 'Unknown')}\n"
                    jira_details += f"• **Priority:** {fields.get('priority', {}).get('name', 'Unknown')}\n"

                    if fields.get('assignee'):
                        jira_details += f"• **Assigned to:** {fields['assignee'].get('displayName', 'Unknown')}\n"

                    if fields.get('description_text'):
                        jira_details += f"• **Description:** {fields['description_text']}\n"

                    if fields.get('comments_text') and len(fields['comments_text']) > 0:
                        jira_details += f"• **Comments ({len(fields['comments_text'])}):**\n"
                        for i, comment in enumerate(fields['comments_text'][:3]):  # Show max 3 comments
                            jira_details += f"  {i+1}. {comment}\n"
                        if len(fields['comments_text']) > 3:
                            jira_details += f"  ... and {len(fields['comments_text'])-3} more comments\n"

                    if fields.get('labels'):
                        jira_details += f"• **Labels:** {', '.join(fields['labels'])}\n"

                    if fields.get('components'):
                        components = [comp.get('name', '') for comp in fields['components']]
                        jira_details += f"• **Components:** {', '.join(components)}\n"

                    jira_details += "\n"
                else:
                    jira_details += f"**{jira_key}** - Failed to retrieve details\n\n"

    # COLUMN 3: ANALYSIS & CLASSIFICATION
    analysis = ""

    if jira_keys_in_row:
        # Check if all JIRA keys in this row are duplicates
        all_duplicates = True
        first_occurrences = []

        for jira_key in jira_keys_in_row:
            first_occurrence_rows = all_jira_keys_in_file.get(jira_key, [])
            first_row = min(first_occurrence_rows) if first_occurrence_rows else current_row_number

            if first_row >= current_row_number:
                all_duplicates = False
            else:
                first_occurrences.append(str(first_row))

        if all_duplicates and first_occurrences:
            analysis = f"**SEE ANALYSIS IN ROWS:** {', '.join(first_occurrences)}"
        else:
            # Prepare JIRA details for analysis
            jira_details_for_analysis = {}
            for jira_key in jira_keys_in_row:
                first_occurrence_rows = all_jira_keys_in_file.get(jira_key, [])
                first_row = min(first_occurrence_rows) if first_occurrence_rows else current_row_number

                if first_row >= current_row_number and jira_key in all_jira_details:
                    jira_details_for_analysis[jira_key] = all_jira_details[jira_key]

            classification = classify_issue_type(simon_comment, description_text, task_text, row_data, jira_details_for_analysis)

            analysis = f"{classification['header']}\n\n"
            analysis += f"**CLASSIFICATION JUSTIFICATION:**\n{classification['explanation']}\n\n"
            analysis += f"**RECOMMENDED ACTIONS:**\n{classification['recommendations']}"

            # Add references to duplicates if any
            if first_occurrences:
                analysis += f"\n\n**OTHER OCCURRENCES:** {', '.join(first_occurrences)}"

    elif simon_comment and simon_comment.strip():
        # Simon's comment without JIRA numbers
        classification = classify_issue_type(simon_comment, description_text, task_text, row_data, {})

        analysis = f"{classification['header']}\n\n"
        analysis += f"**CLASSIFICATION JUSTIFICATION:**\n{classification['explanation']}\n\n"
        analysis += f"**RECOMMENDED ACTIONS:**\n{classification['recommendations']}"

    # COLUMN 4: RELATED RS ISSUES
    rs_service = ""

    if simon_comment and simon_comment.strip():
        # Find contextually related RS issues
        rs_service = find_related_rs_issues(simon_comment, description_text, task_text, row_data, all_jira_details)

    return {
        "source_data": source_data,
        "jira_details": jira_details,
        "analysis": analysis,
        "rs_service": rs_service
    }

def find_related_rs_issues(simon_comment, description_text, task_text, row_data, all_jira_details):
    """Finds contextually related issues from RS project"""

    # Get keywords from Simon's comment and other data
    keywords = []

    # Extract keywords from Simon's comment
    if simon_comment:
        words = re.findall(r'\b\w+\b', simon_comment.lower())
        keywords.extend([word for word in words if len(word) > 3])

    # Add other relevant words from row
    for col, value in row_data.items():
        if pd.notna(value) and isinstance(value, str):
            col_lower = str(col).lower()
            if any(word in col_lower for word in ['description', 'task', 'project']):
                words = re.findall(r'\b\w+\b', str(value).lower())
                keywords.extend([word for word in words if len(word) > 3])

    # Remove duplicates and common words
    common_words = {'this', 'that', 'with', 'from', 'they', 'have', 'been', 'were', 'said', 'each', 'which', 'their', 'time', 'will', 'about', 'would', 'there', 'could', 'other', 'more', 'very', 'what', 'know', 'just', 'first', 'into', 'over', 'think', 'also', 'your', 'work', 'life', 'only', 'can', 'still', 'should', 'after', 'being', 'now', 'made', 'before', 'here', 'through', 'when', 'where', 'much', 'some', 'these', 'many', 'then', 'them', 'well', 'were'}
    keywords = list(set([k for k in keywords if k not in common_words]))

    if not keywords:
        return ""

    # Search in RS issues
    matching_issues = []

    for issue_key, fields in all_jira_details.items():
        if not issue_key.startswith('RS-'):
            continue

        issue_score = 0

        # Prepare text to search
        issue_text = ""
        if fields.get('summary'):
            issue_text += f" {fields['summary']}"

        if fields.get('description_text'):
            issue_text += f" {fields['description_text']}"

        # Add comments
        if fields.get('all_comments_combined'):
            issue_text += f" {fields['all_comments_combined']}"

        issue_text = issue_text.lower()

        # Count keyword matches
        for keyword in keywords:
            if keyword in issue_text:
                issue_score += 1

        if issue_score > 0:
            matching_issues.append({
                'key': issue_key,
                'fields': fields,
                'score': issue_score
            })

    # Sort by score
    matching_issues.sort(key=lambda x: x['score'], reverse=True)

    if not matching_issues:
        return ""

    # Prepare response
    response = "**RELATED ISSUES FROM RS PROJECT:**\n\n"

    for match in matching_issues[:3]:  # Show max 3 best matches
        fields = match['fields']

        response += f"**{match['key']}** (keyword matches: {match['score']})\n"
        response += f"• **Title:** {fields.get('summary', 'No title')}\n"
        response += f"• **Status:** {fields.get('status', {}).get('name', 'Unknown')}\n"
        response += f"• **Type:** {fields.get('issuetype', {}).get('name', 'Unknown')}\n"

        if fields.get('description_text'):
            desc_short = fields['description_text'][:150] + "..." if len(fields['description_text']) > 150 else fields['description_text']
            response += f"• **Description:** {desc_short}\n"

        # Add most important comments
        if fields.get('comments_text'):
            comments = fields['comments_text']
            if comments:
                response += f"• **Comments ({len(comments)}):**\n"
                for i, comment in enumerate(comments[:2]):  # Show max 2 comments
                    comment_short = comment[:100] + "..." if len(comment) > 100 else comment
                    response += f"  {i+1}. {comment_short}\n"

        response += "\n"

    # Add keywords used for search
    response += f"**Keywords used for search:** {', '.join(keywords[:10])}\n"

    return response

def main():
    """Main function"""
    print("Creating complete English analysis from source file...")
    print("Retrieving information from both JIRA repositories (RASEA and RS)...")
    
    # Get all JIRA issues from both projects
    print("\n=== RETRIEVING ALL JIRA ISSUES ===")
    all_jira_issues = get_all_jira_projects_issues()
    
    if not all_jira_issues:
        print("❌ Failed to retrieve JIRA issues")
        return
    
    print(f"Retrieved {len(all_jira_issues)} total issues from both projects")
    
    # Load source file
    source_filename = "clockify_source.xlsx"
    
    try:
        print(f"\n=== LOADING SOURCE FILE ===")
        print(f"Loading file: {source_filename}")
        
        # Check available sheets
        xl_file = pd.ExcelFile(source_filename)
        print(f"Available sheets: {xl_file.sheet_names}")
        
        # Load "Detailed Report" sheet
        if "Detailed Report" in xl_file.sheet_names:
            df = pd.read_excel(source_filename, sheet_name="Detailed Report")
        else:
            print("'Detailed Report' sheet not found. Using first sheet.")
            df = pd.read_excel(source_filename, sheet_name=0)
        
        print(f"Loaded {len(df)} rows")
        print(f"Columns: {list(df.columns)}")
        
        # Find Simon column
        simon_column = None
        for col in df.columns:
            if 'simon' in str(col).lower():
                simon_column = col
                break
        
        if simon_column is None:
            print("Simon column not found")
            return
        
        print(f"Found Simon column: {simon_column}")
        
        # STEP 1: Scan ALL rows to find JIRA numbers
        print(f"\n=== SCANNING FOR JIRA NUMBERS ===")
        all_jira_keys_in_file = {}  # {jira_key: [list_of_rows_where_it_appears]}
        
        for index, row in df.iterrows():
            # Get all text from row
            all_row_text = ""
            for col, value in row.items():
                if pd.notna(value) and isinstance(value, str):
                    all_row_text += f" {value}"
            
            jira_keys = extract_jira_keys(all_row_text)
            
            for jira_key in jira_keys:
                if jira_key not in all_jira_keys_in_file:
                    all_jira_keys_in_file[jira_key] = []
                all_jira_keys_in_file[jira_key].append(index + 1)  # Row number (1-based)
        
        print(f"Found {len(all_jira_keys_in_file)} unique JIRA numbers: {list(all_jira_keys_in_file.keys())}")
        
        # STEP 2: Create analysis columns
        print(f"\n=== CREATING ANALYSIS COLUMNS ===")
        source_data_responses = []
        jira_details_responses = []
        analysis_responses = []
        rs_service_responses = []
        
        for index, row in df.iterrows():
            simon_comment = row.get(simon_column, "")
            
            # Get additional information from row
            description_text = ""
            task_text = ""
            
            # Try to find columns with task description
            for col in df.columns:
                col_lower = str(col).lower()
                if any(word in col_lower for word in ['description', 'task', 'project', 'opis', 'zadanie']):
                    if pd.notna(row.get(col)):
                        if 'description' in col_lower or 'opis' in col_lower:
                            description_text += str(row.get(col, "")) + " "
                        elif 'task' in col_lower or 'zadanie' in col_lower:
                            task_text += str(row.get(col, "")) + " "
            
            # Find JIRA numbers in this row
            all_row_text = f"{simon_comment} {description_text} {task_text}"
            for col, value in row.items():
                if pd.notna(value) and isinstance(value, str):
                    all_row_text += f" {value}"
            
            jira_keys_in_row = extract_jira_keys(all_row_text)
            
            # Generate responses for columns
            if pd.notna(simon_comment) and str(simon_comment).strip():
                print(f"Processing row {index + 1}: {str(simon_comment)[:50]}...")
                
                # Prepare JIRA details for this row
                jira_details_for_row = {}
                for jira_key in jira_keys_in_row:
                    if jira_key in all_jira_issues:
                        jira_details_for_row[jira_key] = all_jira_issues[jira_key]
                
                # Generate analysis
                responses = analyze_row_complete(
                    simon_comment, 
                    description_text, 
                    task_text, 
                    row_data=row.to_dict(),
                    jira_keys_in_row=jira_keys_in_row,
                    all_jira_keys_in_file=all_jira_keys_in_file,
                    all_jira_details=all_jira_issues,
                    current_row_number=index + 1
                )
                
                source_data_responses.append(responses["source_data"])
                jira_details_responses.append(responses["jira_details"])
                analysis_responses.append(responses["analysis"])
                rs_service_responses.append(responses["rs_service"])
                
            elif jira_keys_in_row:
                # Row without Simon comment but with JIRA numbers
                print(f"Row {index + 1}: No Simon comment, but found JIRA: {jira_keys_in_row}")
                
                jira_details_for_row = {}
                for jira_key in jira_keys_in_row:
                    if jira_key in all_jira_issues:
                        jira_details_for_row[jira_key] = all_jira_issues[jira_key]
                
                responses = analyze_row_complete(
                    "", 
                    description_text, 
                    task_text, 
                    row_data=row.to_dict(),
                    jira_keys_in_row=jira_keys_in_row,
                    all_jira_keys_in_file=all_jira_keys_in_file,
                    all_jira_details=all_jira_issues,
                    current_row_number=index + 1
                )
                
                source_data_responses.append(responses["source_data"])
                jira_details_responses.append(responses["jira_details"])
                analysis_responses.append(responses["analysis"])
                rs_service_responses.append(responses["rs_service"])
            else:
                source_data_responses.append("")
                jira_details_responses.append("")
                analysis_responses.append("")
                rs_service_responses.append("")
        
        # Add new columns
        df['Source Data'] = source_data_responses
        df['JIRA Details (RASEA + RS)'] = jira_details_responses
        df['Analysis & Classification'] = analysis_responses
        df['Related RS Issues'] = rs_service_responses
        
        # Save to new file
        output_filename = f"Clockify_Complete_English_Analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        with pd.ExcelWriter(output_filename, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Detailed Report', index=False)
            
            # Adjust column widths
            worksheet = writer.sheets['Detailed Report']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                
                # Special widths for new columns
                if column[0].value in ['Source Data', 'JIRA Details (RASEA + RS)', 'Analysis & Classification', 'Related RS Issues']:
                    adjusted_width = min(max_length + 2, 100)  # Wide columns for analysis
                else:
                    adjusted_width = min(max_length + 2, 30)  # Standard columns
                
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        print(f"\n✅ SUCCESS! File processed: {output_filename}")
        print(f"Added {len([r for r in source_data_responses if r])} rows with analysis")
        print("\n📊 Created 4 analysis columns:")
        print("1. 'Source Data' - all data used for analysis")
        print("2. 'JIRA Details (RASEA + RS)' - details from both JIRA projects")
        print("3. 'Analysis & Classification' - bug/improvement classification with justification")
        print("4. 'Related RS Issues' - contextually related issues from RS project")
        
        print(f"\n🔍 JIRA Statistics:")
        rasea_count = len([k for k in all_jira_issues.keys() if k.startswith('RASEA')])
        rs_count = len([k for k in all_jira_issues.keys() if k.startswith('RS')])
        print(f"- RASEA project: {rasea_count} issues")
        print(f"- RS project: {rs_count} issues")
        print(f"- Total: {len(all_jira_issues)} issues analyzed")
        
        return output_filename
        
    except Exception as e:
        print(f"Error processing file: {e}")
        return None

if __name__ == "__main__":
    main()
