#!/usr/bin/env py
# -*- coding: utf-8 -*-

import pandas as pd
import requests
import json
import base64
import re
from datetime import datetime

# Konfiguracja JIRA
JIRA_URL = "https://read-at-sea.atlassian.net"
EMAIL = "j.wik<PERSON><PERSON>@a-soft.pl"
API_TOKEN = "ATATT3xFfGF0o1tVy4he6mvThd2U0mZU2NCqHbZoCB216R6aPWZZUnKecNaRaTIFIBkmb3sTHDvEPtjnJ__aW1EGolFwrIFPuo5qY9Z4SEW2TlMMt8d8v0sLWKkGGMkjLSDQQyP2wT4BImUn_XaNGozE1-KzMj1GFxhAtdkdmwtMErw0hkwPo4Q=D1D6CC05"

def get_rs_project_issues():
    """Pobiera zadania z projektu RS"""
    
    # Przygotowanie autoryzacji
    auth_string = f"{EMAIL}:{API_TOKEN}"
    auth_bytes = auth_string.encode('ascii')
    auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
    
    headers = {
        'Authorization': f'Basic {auth_b64}',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }
    
    # Pobierz zadania z projektu RS
    url = f"{JIRA_URL}/rest/api/3/search"
    
    all_issues = []
    start_at = 0
    max_results = 50
    
    while True:
        params = {
            'jql': 'project = RS ORDER BY created DESC',
            'startAt': start_at,
            'maxResults': max_results,
            'fields': 'key,summary,status,assignee,reporter,created,updated,priority,issuetype,description,components,labels,comment'
        }
        
        try:
            print(f"Pobieranie zadań RS {start_at + 1}-{start_at + max_results}...")
            response = requests.get(url, headers=headers, params=params)
            response.raise_for_status()
            data = response.json()
            
            issues = data.get('issues', [])
            all_issues.extend(issues)
            
            # Sprawdź czy są jeszcze zadania do pobrania
            total = data.get('total', 0)
            print(f"Pobrano {len(issues)} zadań, łącznie: {len(all_issues)}/{total}")
            
            if start_at + len(issues) >= total or len(issues) == 0:
                break
                
            start_at += max_results
            
        except requests.exceptions.RequestException as e:
            print(f"Błąd podczas pobierania danych z JIRA: {e}")
            if hasattr(e.response, 'text'):
                print(f"Odpowiedź serwera: {e.response.text}")
            break
    
    return all_issues

def extract_text_from_jira_content(content):
    """Wyciąga tekst z struktury JIRA content (JSON)"""
    if not content:
        return ""
    
    if isinstance(content, str):
        return content
    
    if isinstance(content, dict):
        text_parts = []
        
        # Sprawdź czy to struktura Atlassian Document Format
        if content.get('type') == 'doc' and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))
        
        # Sprawdź inne typy elementów
        elif content.get('type') == 'paragraph' and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))
        
        elif content.get('type') == 'text' and 'text' in content:
            text_parts.append(content['text'])
        
        elif content.get('type') in ['heading', 'codeBlock', 'blockquote'] and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))
        
        # Jeśli to lista
        elif content.get('type') in ['bulletList', 'orderedList'] and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))
        
        elif content.get('type') == 'listItem' and 'content' in content:
            for item in content['content']:
                text_parts.append(extract_text_from_jira_content(item))
        
        return ' '.join(text_parts)
    
    elif isinstance(content, list):
        text_parts = []
        for item in content:
            text_parts.append(extract_text_from_jira_content(item))
        return ' '.join(text_parts)
    
    return str(content)

def find_contextual_rs_info(row_data, rs_issues):
    """Znajduje kontekstowe informacje z projektu RS dla danego wiersza"""
    
    # Pobierz teksty z wiersza
    all_row_text = ""
    simon_comment = str(row_data.get('Simon', ''))
    
    for col, value in row_data.items():
        if pd.notna(value) and isinstance(value, str):
            all_row_text += f" {value}"
    
    all_row_text = all_row_text.lower()
    
    # Słowa kluczowe do wyszukiwania kontekstowego
    keywords = []
    
    # Wyciągnij słowa kluczowe z komentarza Simona
    if simon_comment:
        # Usuń znaki interpunkcyjne i podziel na słowa
        words = re.findall(r'\b\w+\b', simon_comment.lower())
        # Filtruj słowa dłuższe niż 3 znaki
        keywords.extend([word for word in words if len(word) > 3])
    
    # Dodaj inne istotne słowa z wiersza
    for col, value in row_data.items():
        if pd.notna(value) and isinstance(value, str):
            col_lower = str(col).lower()
            if any(word in col_lower for word in ['description', 'task', 'project']):
                words = re.findall(r'\b\w+\b', str(value).lower())
                keywords.extend([word for word in words if len(word) > 3])
    
    # Usuń duplikaty i słowa powszechne
    common_words = {'this', 'that', 'with', 'from', 'they', 'have', 'been', 'were', 'said', 'each', 'which', 'their', 'time', 'will', 'about', 'would', 'there', 'could', 'other', 'more', 'very', 'what', 'know', 'just', 'first', 'into', 'over', 'think', 'also', 'your', 'work', 'life', 'only', 'can', 'still', 'should', 'after', 'being', 'now', 'made', 'before', 'here', 'through', 'when', 'where', 'much', 'some', 'these', 'many', 'then', 'them', 'well', 'were'}
    keywords = list(set([k for k in keywords if k not in common_words]))
    
    if not keywords:
        return ""
    
    # Wyszukaj w zadaniach RS
    matching_issues = []
    
    for issue in rs_issues:
        fields = issue.get('fields', {})
        issue_score = 0
        
        # Przygotuj tekst do przeszukiwania
        issue_text = ""
        if fields.get('summary'):
            issue_text += f" {fields['summary']}"
        
        if fields.get('description'):
            desc_text = extract_text_from_jira_content(fields['description'])
            issue_text += f" {desc_text}"
        
        # Dodaj komentarze
        if fields.get('comment', {}).get('comments'):
            for comment in fields['comment']['comments']:
                if comment.get('body'):
                    comment_text = extract_text_from_jira_content(comment['body'])
                    issue_text += f" {comment_text}"
        
        issue_text = issue_text.lower()
        
        # Policz dopasowania słów kluczowych
        for keyword in keywords:
            if keyword in issue_text:
                issue_score += 1
        
        if issue_score > 0:
            matching_issues.append({
                'issue': issue,
                'score': issue_score,
                'key': issue['key']
            })
    
    # Sortuj według wyniku
    matching_issues.sort(key=lambda x: x['score'], reverse=True)
    
    if not matching_issues:
        return ""
    
    # Przygotuj odpowiedź
    response = "**POWIĄZANE ZADANIA Z PROJEKTU RS:**\n\n"
    
    for match in matching_issues[:3]:  # Pokaż maksymalnie 3 najlepsze dopasowania
        issue = match['issue']
        fields = issue['fields']
        
        response += f"**{match['key']}** (dopasowanie: {match['score']} słów kluczowych)\n"
        response += f"• **Tytuł:** {fields.get('summary', 'Brak tytułu')}\n"
        response += f"• **Status:** {fields.get('status', {}).get('name', 'Nieznany')}\n"
        response += f"• **Typ:** {fields.get('issuetype', {}).get('name', 'Nieznany')}\n"
        
        if fields.get('description'):
            desc_text = extract_text_from_jira_content(fields['description'])
            if desc_text:
                desc_short = desc_text[:150] + "..." if len(desc_text) > 150 else desc_text
                response += f"• **Opis:** {desc_short}\n"
        
        # Dodaj najważniejsze komentarze
        if fields.get('comment', {}).get('comments'):
            comments = fields['comment']['comments']
            if comments:
                response += f"• **Komentarze ({len(comments)}):**\n"
                for i, comment in enumerate(comments[:2]):  # Pokaż max 2 komentarze
                    if comment.get('body'):
                        comment_text = extract_text_from_jira_content(comment['body'])
                        author = comment.get('author', {}).get('displayName', 'Nieznany')
                        created = comment.get('created', '')[:10]  # Tylko data
                        comment_short = comment_text[:100] + "..." if len(comment_text) > 100 else comment_text
                        response += f"  {i+1}. [{author}, {created}]: {comment_short}\n"
        
        response += "\n"
    
    # Dodaj słowa kluczowe użyte do wyszukiwania
    response += f"**Słowa kluczowe użyte do wyszukiwania:** {', '.join(keywords[:10])}\n"
    
    return response

def add_rs_service_column():
    """Dodaje kolumnę RS-Service do istniejącego pliku Excel"""
    
    # Pobierz zadania z projektu RS
    print("Pobieranie zadań z projektu RS...")
    rs_issues = get_rs_project_issues()
    
    if not rs_issues:
        print("Nie udało się pobrać zadań z projektu RS")
        return
    
    print(f"Pobrano {len(rs_issues)} zadań z projektu RS")
    
    # Wczytaj istniejący plik Excel
    input_filename = "Clockify_ENGLISH_Translation.xlsx"
    
    try:
        print(f"Wczytywanie pliku: {input_filename}")
        df = pd.read_excel(input_filename, sheet_name='Detailed Report')
        print(f"Wczytano {len(df)} wierszy")
        
        # Utwórz kolumnę RS-Service
        rs_service_responses = []
        
        for index, row in df.iterrows():
            simon_comment = row.get('Simon', '')
            
            if pd.notna(simon_comment) and str(simon_comment).strip():
                print(f"Przetwarzanie wiersza {index + 1}: {str(simon_comment)[:50]}...")
                rs_info = find_contextual_rs_info(row.to_dict(), rs_issues)
                rs_service_responses.append(rs_info)
            else:
                rs_service_responses.append("")
        
        # Dodaj nową kolumnę
        df['RS-Service'] = rs_service_responses
        
        # Zapisz do nowego pliku
        output_filename = f"Clockify_with_RS_Service_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        with pd.ExcelWriter(output_filename, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Detailed Report', index=False)
            
            # Dostosuj szerokość kolumn
            worksheet = writer.sheets['Detailed Report']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                
                # Specjalna szerokość dla kolumny RS-Service
                if column[0].value == 'RS-Service':
                    adjusted_width = min(max_length + 2, 100)  # Szeroka kolumna
                else:
                    adjusted_width = min(max_length + 2, 50)  # Standardowe kolumny
                
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        print(f"Plik został zapisany jako: {output_filename}")
        print(f"Dodano {len([r for r in rs_service_responses if r])} wierszy z informacjami RS-Service")
        
        return output_filename
        
    except Exception as e:
        print(f"Błąd podczas przetwarzania pliku: {e}")
        return None

def main():
    """Główna funkcja"""
    print("Dodawanie kolumny RS-Service z kontekstowymi informacjami z projektu RS...")
    
    output_file = add_rs_service_column()
    
    if output_file:
        print(f"\n✅ Sukces! Plik został przetworzony: {output_file}")
        print("\n📊 Dodano kolumnę 'RS-Service' zawierającą:")
        print("- Kontekstowo powiązane zadania z projektu RS")
        print("- Dopasowania na podstawie słów kluczowych")
        print("- Szczegóły zadań, komentarze i opisy")
        print("- Wyniki posortowane według relevancji")
    else:
        print("\n❌ Wystąpił błąd podczas przetwarzania pliku")

if __name__ == "__main__":
    main()
