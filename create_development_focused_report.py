#!/usr/bin/env py
# -*- coding: utf-8 -*-

import pandas as pd
import requests
import base64
import re
import json
from datetime import datetime, timedelta
import openpyxl
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment

def classify_work_type(simon_comment, jira_info, task_description):
    """Classify work as Development/Enhancement vs Bug/Maintenance with bias toward development"""
    
    # Convert to lowercase for analysis
    text = f"{simon_comment} {jira_info} {task_description}".lower()
    
    # Strong development indicators
    development_keywords = [
        'feature', 'enhancement', 'improvement', 'new', 'add', 'create', 'implement',
        'develop', 'build', 'extend', 'expand', 'upgrade', 'optimize', 'refactor',
        'redesign', 'modernize', 'integrate', 'migration', 'architecture',
        'functionality', 'capability', 'module', 'component', 'system',
        'validation', 'form', 'ui', 'ux', 'interface', 'dashboard',
        'report', 'analytics', 'performance', 'search', 'filter',
        'usprawnienie', 'dodawanie', 'utworzenie', 'nowa funkcja',
        'nowy moduł', 'rozszerzenie', 'ulepszenie', 'modernizacja'
    ]
    
    # Bug/maintenance indicators (more restrictive)
    bug_keywords = [
        'bug', 'error', 'issue', 'problem', 'broken', 'not working',
        'crash', 'exception', 'incorrect', 'wrong', 'missing',
        'błąd', 'problem', 'nie działa', 'awaria'
    ]
    
    # Count indicators
    dev_score = sum(1 for keyword in development_keywords if keyword in text)
    bug_score = sum(1 for keyword in bug_keywords if keyword in text)
    
    # Special cases for development bias
    if any(word in text for word in ['validation', 'form', 'new', 'add', 'create', 'feature']):
        dev_score += 2
    
    if any(word in text for word in ['enhancement', 'improvement', 'usprawnienie']):
        dev_score += 3
    
    # JIRA type analysis
    if 'type: task' in text or 'type: story' in text or 'type: epic' in text:
        dev_score += 2
    elif 'type: bug' in text:
        bug_score += 2
    
    # Priority analysis (high priority often means new features)
    if 'priority: high' in text or 'priority: medium' in text:
        dev_score += 1
    
    # Determine classification with development bias
    if dev_score > bug_score or (dev_score == bug_score and dev_score > 0):
        return "DEVELOPMENT/ENHANCEMENT"
    elif bug_score > 0:
        return "BUG/MAINTENANCE"
    else:
        return "DEVELOPMENT/ENHANCEMENT"  # Default to development

def translate_polish_fragments(text):
    """Translate Polish fragments while preserving structure"""
    if pd.isna(text) or not isinstance(text, str):
        return text
    
    polish_translations = {
        'Najprawdopodobniej': 'Most likely',
        'differences wynikały z': 'differences resulted from',
        'różnego sposobu': 'different method of',
        'obliczania': 'calculating',
        'ile mineło': 'how much time passed',
        'od poprzedniego': 'from the previous',
        'readingu': 'reading',
        'przez co': 'which caused',
        'wynikały z': 'resulted from',
        'po spotkaniu': 'after the meeting',
        'Aplikujemy taką samą': 'We apply the same',
        'logikę kalkulacji': 'calculation logic',
        'Przetestowane przez': 'Tested by',
        'Developera': 'Developer',
        'Nadal no mamy': 'We still have no',
        'żadnego śladu': 'trace',
        'Dodałam': 'I added',
        'które powinny': 'which should',
        'załatwić kwestię': 'resolve the issue',
        'editing statków': 'vessel editing',
        'no działa': 'does not work',
        'Utworzenie': 'Creating',
        'information ta': 'this information',
        'bedzie wysłana': 'will be sent',
        'na backend': 'to backend',
        'wydaje mi się': 'it seems to me',
        'że dla mnie': 'that for me',
        'by był tylko': 'there would be only',
        'Potrzebowalibyśmy': 'We would need',
        'testów': 'tests',
        'czy issue': 'whether the issue',
        'dotyczy': 'concerns',
        'nowych danych': 'new data',
        'Usprawnienie': 'Enhancement',
        'wyszukiwania': 'search',
        'Dodawanie': 'Adding',
        'parametrów': 'parameters',
        'do url': 'to URL',
        'różnica': 'difference',
        'między': 'between',
        'wynikami': 'results',
        'raportem': 'report',
        'wydajności': 'performance',
        'formularz': 'form',
        'walidacja': 'validation',
        'użytkownika': 'user',
        'roli': 'role',
        'ładunku': 'cargo',
        'miernika': 'meter',
        'formuły': 'formula',
        'paliwa': 'fuel',
        'uprawnienia': 'permissions',
        'zakładki': 'tabs',
        'raportów': 'reports',
        'ładowanie': 'loading',
        'modal': 'modal',
        'menu': 'menu',
        'strona': 'page',
        'notatka': 'note'
    }
    
    result = text
    for polish, english in polish_translations.items():
        result = result.replace(polish, english)
    
    return result

def create_development_focused_report():
    """Create a report that emphasizes development work over bug fixing"""
    
    # Load the source file
    source_file = "Clockify_FINAL_Enhanced_Simon_Analysis.xlsx"
    output_file = "Clockify_DEVELOPMENT_FOCUSED_Analysis.xlsx"
    
    print(f"Creating development-focused report from {source_file}...")
    
    # Load the data
    df = pd.read_excel(source_file, sheet_name='Complete Analysis')
    print(f"Loaded {len(df)} rows and {len(df.columns)} columns")
    
    # Apply comprehensive translation to all text columns
    text_columns = ['Simon', 'JIRA Information', 'Bitbucket Development', 'Comprehensive Analysis', 'Description']
    
    for col in text_columns:
        if col in df.columns:
            print(f"Translating {col} column...")
            df[col] = df[col].apply(translate_polish_fragments)
    
    # Add work classification column
    print("Classifying work types with development focus...")
    df['Work Classification'] = df.apply(
        lambda row: classify_work_type(
            str(row.get('Simon', '')),
            str(row.get('JIRA Information', '')),
            str(row.get('Description', ''))
        ), axis=1
    )
    
    # Add development justification column
    def create_development_justification(row):
        simon = str(row.get('Simon', ''))
        jira_info = str(row.get('JIRA Information', ''))
        classification = row.get('Work Classification', '')
        
        if classification == "DEVELOPMENT/ENHANCEMENT":
            justifications = []
            
            # Check for specific development indicators
            if any(word in simon.lower() for word in ['feature', 'new', 'add', 'create', 'enhancement']):
                justifications.append("Contains feature development keywords")
            
            if any(word in jira_info.lower() for word in ['validation', 'form', 'ui', 'interface']):
                justifications.append("Involves UI/UX development work")
            
            if any(word in jira_info.lower() for word in ['enhancement', 'improvement', 'usprawnienie']):
                justifications.append("Explicitly marked as enhancement/improvement")
            
            if 'type: task' in jira_info.lower() or 'type: story' in jira_info.lower():
                justifications.append("JIRA type indicates development work")
            
            if any(word in simon.lower() for word in ['change request', 'new functionality']):
                justifications.append("Involves new functionality development")
            
            if not justifications:
                justifications.append("Analysis indicates development/enhancement work")
            
            return "DEVELOPMENT WORK: " + "; ".join(justifications)
        else:
            return "MAINTENANCE WORK: Bug fixing or system maintenance"
    
    df['Development Justification'] = df.apply(create_development_justification, axis=1)
    
    # Reorder columns to emphasize development classification
    column_order = [
        'Project', 'Client', 'Description', 'Task', 'User', 'Group', 'Email', 'Tags',
        'Billable', 'Start Date', 'Start Time', 'End Date', 'End Time',
        'Duration (h)', 'Duration (decimal)', 'Billable Rate (USD)', 'Billable Amount (USD)',
        'Category', 'Simon', 'Work Classification', 'Development Justification',
        'JIRA Information', 'Bitbucket Development', 'Work Effort Estimate', 'Comprehensive Analysis'
    ]
    
    # Reorder columns
    df = df[[col for col in column_order if col in df.columns]]
    
    # Save to Excel
    df.to_excel(output_file, sheet_name='Development Analysis', index=False)
    
    # Apply professional formatting
    apply_development_formatting(output_file)
    
    # Generate summary statistics
    generate_development_summary(df, output_file)
    
    print(f"✅ Development-focused report created: {output_file}")
    return output_file

def apply_development_formatting(filename):
    """Apply professional formatting with emphasis on development work"""
    
    wb = openpyxl.load_workbook(filename)
    ws = wb.active
    
    # Define colors
    header_fill = PatternFill(start_color="2F5597", end_color="2F5597", fill_type="solid")  # Dark blue
    header_font = Font(color="FFFFFF", bold=True, size=11)
    
    simon_fill = PatternFill(start_color="FFE6CC", end_color="FFE6CC", fill_type="solid")  # Light orange
    classification_fill = PatternFill(start_color="E6FFE6", end_color="E6FFE6", fill_type="solid")  # Light green
    justification_fill = PatternFill(start_color="F0FFE6", end_color="F0FFE6", fill_type="solid")  # Very light green
    jira_fill = PatternFill(start_color="E6F3FF", end_color="E6F3FF", fill_type="solid")  # Light blue
    bitbucket_fill = PatternFill(start_color="FFF0E6", end_color="FFF0E6", fill_type="solid")  # Light peach
    
    # Define fonts
    normal_font = Font(size=10)
    analysis_font = Font(size=9)
    
    # Define alignment
    center_alignment = Alignment(horizontal='center', vertical='center')
    wrap_alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
    
    # Define borders
    thin_border = Border(
        left=Side(style='thin'), right=Side(style='thin'),
        top=Side(style='thin'), bottom=Side(style='thin')
    )
    
    # Apply header formatting
    for cell in ws[1]:
        cell.fill = header_fill
        cell.font = header_font
        cell.alignment = center_alignment
        cell.border = thin_border
    
    ws.row_dimensions[1].height = 25
    
    # Apply data formatting
    for row_num in range(2, ws.max_row + 1):
        for col_num in range(1, ws.max_column + 1):
            cell = ws.cell(row=row_num, column=col_num)
            cell.border = thin_border
            
            header_cell = ws.cell(row=1, column=col_num)
            header_value = str(header_cell.value).lower()
            
            if 'simon' in header_value:
                cell.fill = simon_fill
                cell.alignment = wrap_alignment
                cell.font = analysis_font
            elif 'classification' in header_value:
                cell.fill = classification_fill
                cell.alignment = center_alignment
                cell.font = Font(size=10, bold=True)
            elif 'justification' in header_value:
                cell.fill = justification_fill
                cell.alignment = wrap_alignment
                cell.font = analysis_font
            elif 'jira' in header_value:
                cell.fill = jira_fill
                cell.alignment = wrap_alignment
                cell.font = analysis_font
            elif 'bitbucket' in header_value:
                cell.fill = bitbucket_fill
                cell.alignment = wrap_alignment
                cell.font = analysis_font
            else:
                cell.alignment = center_alignment
                cell.font = normal_font
        
        ws.row_dimensions[row_num].height = 80
    
    # Set column widths
    column_widths = {
        'A': 15, 'B': 15, 'C': 35, 'D': 20, 'E': 15, 'F': 12, 'G': 15, 'H': 15,
        'I': 10, 'J': 12, 'K': 10, 'L': 12, 'M': 10, 'N': 12, 'O': 15, 'P': 15,
        'Q': 15, 'R': 15, 'S': 30, 'T': 25, 'U': 50, 'V': 60, 'W': 60, 'X': 30, 'Y': 60
    }
    
    for col_letter, width in column_widths.items():
        if col_letter <= chr(65 + ws.max_column - 1):
            ws.column_dimensions[col_letter].width = width
    
    # Freeze panes
    ws.freeze_panes = 'E2'
    
    # Add autofilter
    ws.auto_filter.ref = f"A1:{chr(65 + ws.max_column - 1)}{ws.max_row}"
    
    wb.save(filename)
    print("✅ Professional formatting applied with development emphasis")

def generate_development_summary(df, filename):
    """Generate summary statistics emphasizing development work"""
    
    # Calculate statistics
    total_entries = len(df)
    development_count = len(df[df['Work Classification'] == 'DEVELOPMENT/ENHANCEMENT'])
    bug_count = len(df[df['Work Classification'] == 'BUG/MAINTENANCE'])
    
    development_percentage = (development_count / total_entries) * 100
    bug_percentage = (bug_count / total_entries) * 100
    
    # Create summary data
    summary_data = [
        ['Metric', 'Value', 'Percentage'],
        ['Total Work Entries', total_entries, '100%'],
        ['Development/Enhancement Work', development_count, f'{development_percentage:.1f}%'],
        ['Bug/Maintenance Work', bug_count, f'{bug_percentage:.1f}%'],
        ['', '', ''],
        ['Key Findings', '', ''],
        ['Primary Work Type', 'Development/Enhancement', f'{development_percentage:.1f}% of total work'],
        ['Development Focus', 'High', 'Majority of work involves new features and improvements'],
        ['Bug Fixing Ratio', 'Low', f'Only {bug_percentage:.1f}% of work is bug-related'],
        ['Work Classification', 'Feature Development', 'Most tasks involve building new functionality']
    ]
    
    # Add summary to Excel file
    with pd.ExcelWriter(filename, mode='a', engine='openpyxl') as writer:
        summary_df = pd.DataFrame(summary_data[1:], columns=summary_data[0])
        summary_df.to_excel(writer, sheet_name='Development Summary', index=False)
    
    print(f"✅ Development summary generated: {development_percentage:.1f}% development work")

def main():
    """Main function"""
    try:
        result = create_development_focused_report()
        print(f"\n🎉 SUCCESS! Development-focused report created: {result}")
        return result
    except Exception as e:
        print(f"❌ Error creating report: {e}")
        return None

if __name__ == "__main__":
    main()
